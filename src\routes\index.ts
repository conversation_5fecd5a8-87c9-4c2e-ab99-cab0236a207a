import { Router } from 'express';
import authRoutes from './authRoutes';
import appointmentRoutes from './appointmentRoutes';
import consultationRoutes from './consultationRoutes';
import serviceRoutes from './serviceRoutes';
import productRoutes from './productRoutes';
import cartRoutes from './cartRoutes';
import orderRoutes from './orderRoutes';
import userRoutes from './userRoutes';
import reviewRoutes from './reviewRoutes';
import paymentConfirmationRoutes from './paymentConfirmationRoutes';
import notificationRoutes from './notificationRoutes';
import adminRoutes from './adminRoutes';
import brandingRoutes from './brandingRoutes';
import paymentRoutes from './paymentRoutes';
import businessRoutes from './businessRoutes';
import contentRoutes from './contentRoutes';
import testimonialRoutes from './testimonialRoutes';
import discountRoutes from './discountRoutes';
import analyticsRoutes from './analyticsRoutes';
import uploadRoutes from './uploadRoutes';
import seoRoutes from './seoRoutes';
import userRoleRoutes from './userRoleRoutes';
import giftCardRoutes from './giftCardRoutes';
import loyaltyRoutes from './loyaltyRoutes';
import extendedRoutes from './extendedRoutes';
import categoryRoutes from './categoryRoutes';
import emailRoutes from './emailRoutes';
import { BrandingController } from '../controllers';

const router = Router();

// Mount all routes
router.use('/auth', authRoutes);
router.use('/appointments', appointmentRoutes);
router.use('/consultation', consultationRoutes);
router.use('/services', serviceRoutes);
router.use('/products', productRoutes);
router.use('/cart', cartRoutes);
router.use('/orders', orderRoutes);
router.use('/users', userRoutes);
router.use('/reviews', reviewRoutes); // Enhanced reviews system
router.use('/products', reviewRoutes); // Legacy reviews support
router.use('/payment-confirmations', paymentConfirmationRoutes);
router.use('/notifications', notificationRoutes);
router.use('/admin', adminRoutes);
router.use('/branding', brandingRoutes);
router.use('/categories', categoryRoutes);

// Aggregated branding endpoint (single request for all branding data)
router.get('/branding/complete', BrandingController.getCompleteBranding);

// Individual branding endpoints (kept for backward compatibility)
router.get('/business-profile', BrandingController.getBusinessProfile);
router.get('/theme-settings', BrandingController.getThemeSettings);
router.get('/site-settings', BrandingController.getSiteSettings);
router.use('/admin', paymentRoutes);
router.use('/', businessRoutes); // Business hours at root level
router.use('/admin', businessRoutes); // Admin business routes
router.use('/content', contentRoutes);
router.use('/testimonials', testimonialRoutes);
router.use('/discount-codes', discountRoutes);
router.use('/admin/discount-codes', discountRoutes);
router.use('/admin/analytics', analyticsRoutes);
router.use('/upload', uploadRoutes);
router.use('/admin/seo', seoRoutes);
router.use('/admin/users', userRoleRoutes);
router.use('/gift-cards', giftCardRoutes);
router.use('/users/loyalty', loyaltyRoutes);
router.use('/loyalty', loyaltyRoutes);
router.use('/admin/emails', emailRoutes);
router.use('/', extendedRoutes); // All extended routes

// Health check endpoint
router.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

export default router;
