"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const controllers_1 = require("../controllers");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const validation_2 = require("../utils/validation");
const router = (0, express_1.Router)();
// GET /api/cart
router.get('/', auth_1.authenticate, controllers_1.CartController.getCart);
// POST /api/cart/items
router.post('/items', auth_1.authenticate, (0, validation_1.validate)(validation_2.addToCartValidation), controllers_1.CartController.addToCart);
// PUT /api/cart/items/:itemId
router.put('/items/:itemId', auth_1.authenticate, (0, validation_1.validate)(validation_2.updateCartItemValidation), controllers_1.CartController.updateCartItem);
// DELETE /api/cart/items/:itemId
router.delete('/items/:itemId', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)('itemId')), controllers_1.CartController.removeFromCart);
// DELETE /api/cart
router.delete('/', auth_1.authenticate, controllers_1.CartController.clearCart);
exports.default = router;
