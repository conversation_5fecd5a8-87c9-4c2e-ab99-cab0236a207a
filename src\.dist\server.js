"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("./config/database");
const config_1 = require("./config");
const routes_1 = __importDefault(require("./routes"));
const errorHandler_1 = require("./middleware/errorHandler");
const initializeServer_1 = require("./utils/initializeServer");
const scheduledEmailService_1 = require("./services/scheduledEmailService");
// Load environment variables
dotenv_1.default.config();
// Create Express app
const app = (0, express_1.default)();
// Trust proxy
app.set('trust proxy', 1);
// Security middleware
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));
// CORS configuration - Allow all origins
app.use((0, cors_1.default)({
    origin: true, // Allow all origins
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin',
        'Access-Control-Request-Method',
        'Access-Control-Request-Headers'
    ],
    exposedHeaders: ['Content-Length', 'X-Foo', 'X-Bar'],
    optionsSuccessStatus: 200, // For legacy browser support
    preflightContinue: false
}));
// Compression middleware
app.use((0, compression_1.default)());
// Logging middleware
if (config_1.config.NODE_ENV === 'development') {
    app.use((0, morgan_1.default)('dev'));
}
else {
    app.use((0, morgan_1.default)('combined'));
}
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Static files (for uploaded files)
app.use('/uploads', express_1.default.static('uploads'));
// Additional CORS headers middleware - Allow all origins
app.use((req, res, next) => {
    const origin = req.headers.origin;
    console.log(`🌐 CORS Request - Origin: ${origin}, Method: ${req.method}, Path: ${req.path}`);
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    }
    else {
        next();
    }
});
// Handle preflight requests explicitly
app.options('*', (0, cors_1.default)());
// API routes
app.use('/api', routes_1.default);
// Root endpoint
app.get('/', (_req, res) => {
    res.json({
        success: true,
        message: 'Welcome to MicroLocs Backend API',
        version: '1.0.0',
        documentation: '/api/health',
        endpoints: {
            auth: '/api/auth',
            appointments: '/api/appointments',
            services: '/api/services',
            products: '/api/products',
            cart: '/api/cart',
            orders: '/api/orders',
            users: '/api/users',
            notifications: '/api/notifications',
            admin: '/api/admin'
        }
    });
});
// 404 handler
app.use(errorHandler_1.notFound);
// Error handling middleware
app.use(errorHandler_1.errorHandler);
// Start server
const startServer = async () => {
    try {
        // Connect to database
        await (0, database_1.connectDatabase)();
        // Initialize server (create default admin, etc.)
        await (0, initializeServer_1.initializeServer)();
        // Start listening
        const PORT = Number(config_1.config.PORT) || 3000;
        const HOST = config_1.config.HOST || '0.0.0.0';
        app.listen(PORT, HOST, () => {
            console.log(`
🚀 Server is running on ${HOST}:${PORT}
📊 Environment: ${config_1.config.NODE_ENV}
🔗 Local API URL: http://localhost:${PORT}
🌐 Public API URL: http://${HOST}:${PORT}
📚 Health Check: http://${HOST}:${PORT}/api/health
🔧 Admin Panel: http://${HOST}:${PORT}/api/admin
      `);
            // Initialize scheduled email service
            try {
                scheduledEmailService_1.scheduledEmailService.init();
                console.log('📧 Email scheduling service initialized');
            }
            catch (error) {
                console.error('Failed to initialize email scheduling service:', error);
            }
        });
    }
    catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
};
// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
    console.error('Unhandled Promise Rejection:', err);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    process.exit(1);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    process.exit(0);
});
// Start the server
startServer();
exports.default = app;
