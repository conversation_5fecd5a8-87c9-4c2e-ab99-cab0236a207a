import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { sendSuccess, sendError } from '../utils/response';
import { config } from '../config';
import {
  uploadSingle,
  uploadMultiple,
  uploadFields,
  uploadSingleFile,
  uploadMultipleFiles,
  deleteFromCloudinary,
  getOptimizedImageUrl,
  validateCloudinaryConfig,
  UploadType
} from '../services/cloudinaryService';

// Legacy local storage configuration (kept for backward compatibility)
const localStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = config.UPLOAD.UPLOAD_PATH;

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

// Legacy file filter
const legacyFileFilter = (req: any, file: any, cb: any) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'), false);
  }
};

// Legacy multer configuration
const legacyUpload = multer({
  storage: localStorage,
  fileFilter: legacyFileFilter,
  limits: {
    fileSize: config.UPLOAD.MAX_FILE_SIZE // 5MB default
  }
});

export class UploadController {
  // Legacy middleware for backward compatibility
  static uploadMiddleware = legacyUpload.single('image');

  // Cloudinary middleware
  static cloudinaryUploadSingle = (fieldName: string = 'image') => uploadSingle(fieldName);
  static cloudinaryUploadMultiple = (fieldName: string = 'images', maxCount: number = 10) => uploadMultiple(fieldName, maxCount);

  // Check Cloudinary configuration
  static async checkCloudinaryConfig(req: Request, res: Response): Promise<void> {
    try {
      const isConfigured = validateCloudinaryConfig();

      if (isConfigured) {
        sendSuccess(res, 'Cloudinary is properly configured');
      } else {
        sendError(res, 'Cloudinary configuration is missing or incomplete');
      }
    } catch (error) {
      console.error('Cloudinary config check error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Upload single file to Cloudinary with type parameter
  static async uploadSingleFileToCloudinary(req: Request, res: Response): Promise<void> {
    try {
      const uploadType = req.params.uploadType as UploadType;

      // Validate upload type
      const validUploadTypes: UploadType[] = [
        'profilePicture', 'productImage', 'serviceImage', 'brandingImage',
        'testimonialImage', 'staffImage', 'businessDocs', 'logo',
        'favicon', 'heroImage', 'galleryImage'
      ];

      if (!validUploadTypes.includes(uploadType)) {
        sendError(res, `Invalid upload type. Valid types: ${validUploadTypes.join(', ')}`);
        return;
      }

      if (!req.file) {
        sendError(res, 'No file provided');
        return;
      }

      const fileUrl = await uploadSingleFile(req, uploadType);

      if (!fileUrl) {
        sendError(res, 'Failed to upload file to Cloudinary');
        return;
      }

      sendSuccess(res, `${uploadType} uploaded successfully to Cloudinary`, {
        originalName: req.file.originalname,
        size: req.file.size,
        url: fileUrl,
        uploadType
      });
    } catch (error) {
      console.error('Upload single file to Cloudinary error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Legacy local upload (kept for backward compatibility)
  static async uploadImage(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file) {
        sendError(res, 'No image file provided');
        return;
      }

      const imageUrl = `/uploads/${req.file.filename}`;

      sendSuccess(res, 'Image uploaded successfully', {
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        url: imageUrl,
        fullUrl: `${req.protocol}://${req.get('host')}${imageUrl}`
      });
    } catch (error) {
      console.error('Upload image error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Upload multiple files to Cloudinary with type parameter
  static async uploadMultipleFilesToCloudinary(req: Request, res: Response): Promise<void> {
    try {
      const uploadType = req.params.uploadType as UploadType;

      // Validate upload type
      const validUploadTypes: UploadType[] = [
        'profilePicture', 'productImage', 'serviceImage', 'brandingImage',
        'testimonialImage', 'staffImage', 'businessDocs', 'logo',
        'favicon', 'heroImage', 'galleryImage'
      ];

      if (!validUploadTypes.includes(uploadType)) {
        sendError(res, `Invalid upload type. Valid types: ${validUploadTypes.join(', ')}`);
        return;
      }

      const files = req.files as Express.Multer.File[];

      if (!files || files.length === 0) {
        sendError(res, 'No files provided');
        return;
      }

      const fileUrls = await uploadMultipleFiles(req, uploadType);

      if (fileUrls.length === 0) {
        sendError(res, 'Failed to upload files to Cloudinary');
        return;
      }

      const uploadedFiles = files.map((file, index) => ({
        originalName: file.originalname,
        size: file.size,
        url: fileUrls[index] || null
      })).filter(file => file.url !== null);

      sendSuccess(res, `${uploadType} files uploaded successfully to Cloudinary`, {
        files: uploadedFiles,
        count: uploadedFiles.length,
        uploadType
      });
    } catch (error) {
      console.error('Upload multiple files to Cloudinary error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Legacy multiple upload (kept for backward compatibility)
  static async uploadMultipleImages(req: Request, res: Response): Promise<void> {
    try {
      const uploadMultiple = legacyUpload.array('images', 10); // Max 10 images

      uploadMultiple(req, res, (err) => {
        if (err) {
          console.error('Upload multiple images error:', err);
          sendError(res, err.message);
          return;
        }

        const files = req.files as Express.Multer.File[];

        if (!files || files.length === 0) {
          sendError(res, 'No image files provided');
          return;
        }

        const uploadedImages = files.map(file => ({
          filename: file.filename,
          originalName: file.originalname,
          size: file.size,
          url: `/uploads/${file.filename}`,
          fullUrl: `${req.protocol}://${req.get('host')}/uploads/${file.filename}`
        }));

        sendSuccess(res, 'Images uploaded successfully', {
          images: uploadedImages,
          count: uploadedImages.length
        });
      });
    } catch (error) {
      console.error('Upload multiple images error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Delete image from Cloudinary
  static async deleteImageFromCloudinary(req: Request, res: Response): Promise<void> {
    try {
      const { imageUrl } = req.body;

      if (!imageUrl) {
        sendError(res, 'Image URL is required');
        return;
      }

      const deleted = await deleteFromCloudinary(imageUrl);

      if (deleted) {
        sendSuccess(res, 'Image deleted successfully from Cloudinary');
      } else {
        sendError(res, 'Failed to delete image from Cloudinary');
      }
    } catch (error) {
      console.error('Delete image from Cloudinary error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Get optimized image URL
  static async getOptimizedImage(req: Request, res: Response): Promise<void> {
    try {
      const { imageUrl } = req.params;
      const { width, height, quality, format, crop } = req.query;

      if (!imageUrl) {
        sendError(res, 'Image URL is required');
        return;
      }

      const optimizedUrl = getOptimizedImageUrl(decodeURIComponent(imageUrl), {
        width: width ? parseInt(width as string) : undefined,
        height: height ? parseInt(height as string) : undefined,
        quality: quality as string,
        format: format as string,
        crop: crop as string,
      });

      sendSuccess(res, 'Optimized image URL generated', {
        originalUrl: imageUrl,
        optimizedUrl
      });
    } catch (error) {
      console.error('Get optimized image error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Legacy delete (kept for backward compatibility)
  static async deleteImage(req: Request, res: Response): Promise<void> {
    try {
      const { filename } = req.params;

      if (!filename) {
        sendError(res, 'Filename is required');
        return;
      }

      const filePath = path.join(config.UPLOAD.UPLOAD_PATH, filename);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        sendError(res, 'File not found', undefined, 404);
        return;
      }

      // Delete file
      fs.unlinkSync(filePath);

      sendSuccess(res, 'Image deleted successfully');
    } catch (error) {
      console.error('Delete image error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getImageInfo(req: Request, res: Response): Promise<void> {
    try {
      const { filename } = req.params;
      
      if (!filename) {
        sendError(res, 'Filename is required');
        return;
      }

      const filePath = path.join(config.UPLOAD.UPLOAD_PATH, filename);

      // Check if file exists
      if (!fs.existsSync(filePath)) {
        sendError(res, 'File not found', undefined, 404);
        return;
      }

      const stats = fs.statSync(filePath);
      const imageUrl = `/uploads/${filename}`;

      sendSuccess(res, 'Image info retrieved successfully', {
        filename,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        url: imageUrl,
        fullUrl: `${req.protocol}://${req.get('host')}${imageUrl}`
      });
    } catch (error) {
      console.error('Get image info error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Note: Specialized upload methods removed in favor of unified routes with parameters
  // Use: POST /api/upload/cloudinary/single/:uploadType instead
  // Use: POST /api/upload/cloudinary/multiple/:uploadType instead
}
