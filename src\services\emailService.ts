import nodemailer from 'nodemailer';
import { User } from '../models/User';
import { Appointment } from '../models/Appointment';
import { Branding } from '../models/Branding';

// Type definitions
type UserType = {
  _id?: any;
  firstName?: string;
  lastName?: string;
  name: string;
  email: string;
  phone?: string;
  role?: string;
};

type AppointmentType = {
  _id?: any;
  service: string;
  date: Date;
  time: string;
  duration?: number;
  notes?: string;
  user?: any;
};

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private brandingCache: any = null;
  private brandingCacheTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.transporter = this.createTransporter();
  }

  // Get branding data with caching
  private async getBrandingData(): Promise<any> {
    const now = Date.now();

    // Return cached data if still valid
    if (this.brandingCache && (now - this.brandingCacheTime) < this.CACHE_DURATION) {
      return this.brandingCache;
    }

    try {
      // Fetch fresh branding data from database
      let branding = await Branding.findOne();

      if (!branding) {
        // Create default branding if none exists
        branding = await Branding.create({});
      }

      // Cache the branding data
      this.brandingCache = branding;
      this.brandingCacheTime = now;

      return branding;
    } catch (error) {
      console.error('Error fetching branding data for emails:', error);

      // Return cached data if available, otherwise return minimal defaults
      if (this.brandingCache) {
        return this.brandingCache;
      }

      return {
        global: { siteName: 'MicroLocs', email: '', phone: '', address: '' },
        business: { name: 'MicroLocs', tagline: '', description: '' },
        theme: { colors: { primary: '#f3d016', secondary: '#000000' } }
      };
    }
  }

  private createTransporter(): nodemailer.Transporter {
    const config: EmailConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || ''
      }
    };

    return nodemailer.createTransport(config);
  }

  private async getBaseTemplate(content: string, title: string): Promise<string> {
    const branding = await this.getBrandingData();
    // Extract branding data with fallbacks
    const siteName = branding.global?.siteName || branding.business?.name || 'MicroLocs';
    const tagline = branding.global?.tagline || branding.business?.tagline || 'Your Beauty, Our Passion';
    const phone = branding.global?.phone || branding.business?.phone || '';
    const email = branding.global?.email || branding.business?.email || '';
    const address = branding.global?.address || branding.business?.address || '';
    const logo = branding.global?.logo || '';

    // Theme colors with fallbacks
    const primaryColor = branding.theme?.colors?.primary || '#f3d016';
    const secondaryColor = branding.theme?.colors?.secondary || '#e6c200';

    // Social media links
    const instagram = branding.global?.instagram || branding.business?.social?.instagram || '';
    const facebook = branding.global?.facebook || branding.business?.social?.facebook || '';
    const twitter = branding.global?.twitter || branding.business?.social?.twitter || '';
    const youtube = branding.global?.youtube || branding.business?.social?.youtube || '';

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; }
            .container { max-width: 600px; margin: 0 auto; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColor} 100%); padding: 30px; text-align: center; color: white; }
            .header h1 { font-size: 28px; font-weight: 700; margin-bottom: 8px; }
            .header p { font-size: 16px; opacity: 0.9; }
            .logo { max-width: 150px; height: auto; margin-bottom: 10px; }
            .content { padding: 40px 30px; }
            .content h2 { color: #2c3e50; margin-bottom: 20px; font-size: 24px; }
            .content p { margin-bottom: 16px; color: #555; font-size: 16px; }
            .highlight { background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid ${primaryColor}; margin: 20px 0; }
            .button { display: inline-block; background: ${primaryColor}; color: #333; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; transition: all 0.3s ease; }
            .button:hover { background: ${secondaryColor}; transform: translateY(-2px); }
            .footer { background: #2c3e50; color: white; padding: 25px; text-align: center; }
            .footer p { margin-bottom: 8px; }
            .social-links { margin-top: 15px; }
            .social-links a { color: ${primaryColor}; text-decoration: none; margin: 0 10px; }
            .appointment-card { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .appointment-card h3 { color: #2c3e50; margin-bottom: 15px; }
            .appointment-detail { display: flex; justify-content: space-between; margin-bottom: 8px; }
            .appointment-detail strong { color: #2c3e50; }
        </style>
    </head>
    <body>
        <div class="container">
            ${content}
            <div class="footer">
                ${logo ? `<img src="${logo}" alt="${siteName}" class="logo" style="max-width: 100px; height: auto; margin-bottom: 10px;" />` : ''}
                <p><strong>${siteName}</strong></p>
                ${tagline ? `<p>${tagline}</p>` : ''}
                <p>
                    ${address ? `📍 ${address}<br>` : ''}
                    ${email ? `📧 ${email}` : ''}
                    ${phone && email ? ' | ' : ''}
                    ${phone ? `📞 ${phone}` : ''}
                </p>
                <div class="social-links">
                    ${facebook ? `<a href="${facebook}" target="_blank">Facebook</a>` : ''}
                    ${facebook && (instagram || twitter || youtube) ? ' | ' : ''}
                    ${instagram ? `<a href="${instagram}" target="_blank">Instagram</a>` : ''}
                    ${instagram && (twitter || youtube) ? ' | ' : ''}
                    ${twitter ? `<a href="${twitter}" target="_blank">Twitter</a>` : ''}
                    ${twitter && youtube ? ' | ' : ''}
                    ${youtube ? `<a href="${youtube}" target="_blank">YouTube</a>` : ''}
                </div>
                <p style="margin-top: 15px; font-size: 12px; opacity: 0.7;">
                    You're receiving this email because you're a valued customer of ${siteName}.
                </p>
            </div>
        </div>
    </body>
    </html>
    `;
  }

  // Welcome email for new users
  async sendWelcomeEmail(user: UserType, password?: string): Promise<void> {
    const branding = await this.getBrandingData();
    const siteName = branding.global?.siteName || branding.business?.name || 'MicroLocs';
    const tagline = branding.global?.tagline || branding.business?.tagline || 'Your beauty journey starts here';
    const description = branding.business?.description || 'We specialize in premium beauty and wellness services';
    const logo = branding.global?.logo || '';

    const content = `
      <div class="header">
          ${logo ? `<img src="${logo}" alt="${siteName}" class="logo" />` : ''}
          <h1>Welcome to ${siteName}!</h1>
          <p>${tagline}</p>
      </div>
      <div class="content">
          <h2>Hello ${user.firstName || user.name}! 👋</h2>
          <p>We're thrilled to welcome you to the ${siteName} family! Your account has been successfully created, and you're now part of our exclusive beauty community.</p>

          ${password ? `
          <div class="highlight">
              <strong>Your Account Details:</strong><br>
              Email: ${user.email}<br>
              Temporary Password: <strong>${password}</strong><br>
              <small>Please change your password after your first login for security.</small>
          </div>
          ` : ''}

          <p>${description}</p>

          <p>Ready to book your first appointment? Our expert team is waiting to help you look and feel your absolute best!</p>

          <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/consultation" class="button">Book Your First Consultation</a>

          <p>If you have any questions, our friendly team is always here to help. Don't hesitate to reach out!</p>
      </div>
    `;

    const template: EmailTemplate = {
      subject: `🌟 Welcome to ${siteName} - Your Beauty Journey Begins!`,
      html: await this.getBaseTemplate(content, `Welcome to ${siteName}`),
      text: `Welcome to ${siteName}, ${user.firstName || user.name}! Your account has been created successfully. ${password ? `Your temporary password is: ${password}` : ''} Visit ${process.env.FRONTEND_URL || 'http://localhost:5173'} to get started.`
    };

    await this.sendEmail(user.email, template);
  }

  // Appointment confirmation email
  async sendAppointmentConfirmation(user: UserType, appointment: AppointmentType): Promise<void> {
    const branding = await this.getBrandingData();
    const siteName = branding.global?.siteName || branding.business?.name || 'MicroLocs';
    const phone = branding.global?.phone || branding.business?.phone || '';
    const address = branding.global?.address || branding.business?.address || '';
    const appointmentDate = new Date(appointment.date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const content = `
      <div class="header">
          <h1>Appointment Confirmed! ✅</h1>
          <p>We can't wait to see you</p>
      </div>
      <div class="content">
          <h2>Hello ${user.firstName || user.name}!</h2>
          <p>Great news! Your appointment with ${siteName} has been successfully confirmed. We're excited to provide you with an amazing beauty experience.</p>
          
          <div class="appointment-card">
              <h3>📅 Appointment Details</h3>
              <div class="appointment-detail">
                  <span>Service:</span>
                  <strong>${appointment.service}</strong>
              </div>
              <div class="appointment-detail">
                  <span>Date:</span>
                  <strong>${appointmentDate}</strong>
              </div>
              <div class="appointment-detail">
                  <span>Time:</span>
                  <strong>${appointment.time}</strong>
              </div>
              <div class="appointment-detail">
                  <span>Duration:</span>
                  <strong>${appointment.duration} minutes</strong>
              </div>
              ${appointment.notes ? `
              <div class="appointment-detail">
                  <span>Notes:</span>
                  <strong>${appointment.notes}</strong>
              </div>
              ` : ''}
          </div>
          
          <div class="highlight">
              ${address ? `<strong>📍 Location:</strong> ${address}<br>` : ''}
              ${phone ? `<strong>📞 Contact:</strong> ${phone}<br>` : ''}
              <strong>⏰ Please arrive 10 minutes early</strong>
          </div>
          
          <p>Need to reschedule or have questions? No problem! Just give us a call or manage your appointment online.</p>
          
          <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/dashboard" class="button">Manage Appointment</a>
      </div>
    `;

    const template: EmailTemplate = {
      subject: `✅ Appointment Confirmed - ${appointmentDate} at ${appointment.time}`,
      html: await this.getBaseTemplate(content, 'Appointment Confirmed'),
      text: `Your appointment for ${appointment.service} on ${appointmentDate} at ${appointment.time} has been confirmed. Please arrive 10 minutes early. Contact us at (************* if you need to reschedule.`
    };

    await this.sendEmail(user.email, template);
  }

  // Consultation booking confirmation
  async sendConsultationConfirmation(user: UserType, consultationData: any): Promise<void> {
    const consultationDate = new Date(consultationData.date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const content = `
      <div class="header">
          <h1>Consultation Booked! 💬</h1>
          <p>Let's discuss your beauty goals</p>
      </div>
      <div class="content">
          <h2>Hello ${user.firstName || user.name}!</h2>
          <p>Thank you for booking a consultation with us! We're excited to learn about your beauty goals and help you achieve the perfect look.</p>
          
          <div class="appointment-card">
              <h3>💬 Consultation Details</h3>
              <div class="appointment-detail">
                  <span>Service Interest:</span>
                  <strong>${consultationData.service}</strong>
              </div>
              <div class="appointment-detail">
                  <span>Date:</span>
                  <strong>${consultationDate}</strong>
              </div>
              <div class="appointment-detail">
                  <span>Time:</span>
                  <strong>${consultationData.time}</strong>
              </div>
              ${consultationData.message ? `
              <div class="appointment-detail">
                  <span>Your Message:</span>
                  <strong>${consultationData.message}</strong>
              </div>
              ` : ''}
          </div>
          
          <p>During your consultation, we'll:</p>
          <ul style="margin: 20px 0; padding-left: 20px;">
              <li>🎯 Discuss your specific beauty goals</li>
              <li>💡 Provide expert recommendations</li>
              <li>📋 Create a personalized treatment plan</li>
              <li>💰 Discuss pricing and packages</li>
          </ul>
          
          <div class="highlight">
              <strong>What to expect:</strong> Our consultation is completely free and typically takes 15-30 minutes. Come with any questions or inspiration photos!
          </div>
          
          <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/dashboard" class="button">View My Consultations</a>
      </div>
    `;

    const template: EmailTemplate = {
      subject: `💬 Consultation Confirmed - ${consultationDate} at ${consultationData.time}`,
      html: await this.getBaseTemplate(content, 'Consultation Confirmed'),
      text: `Your consultation for ${consultationData.service} on ${consultationDate} at ${consultationData.time} has been confirmed. We look forward to discussing your beauty goals!`
    };

    await this.sendEmail(user.email, template);
  }

  // Appointment reminder email (24 hours before)
  async sendAppointmentReminder(user: UserType, appointment: AppointmentType): Promise<void> {
    const appointmentDate = new Date(appointment.date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const content = `
      <div class="header">
          <h1>Appointment Reminder ⏰</h1>
          <p>See you tomorrow!</p>
      </div>
      <div class="content">
          <h2>Hello ${user.firstName || user.name}!</h2>
          <p>This is a friendly reminder about your upcoming appointment with us tomorrow. We're looking forward to seeing you!</p>

          <div class="appointment-card">
              <h3>📅 Tomorrow's Appointment</h3>
              <div class="appointment-detail">
                  <span>Service:</span>
                  <strong>${appointment.service}</strong>
              </div>
              <div class="appointment-detail">
                  <span>Date:</span>
                  <strong>${appointmentDate}</strong>
              </div>
              <div class="appointment-detail">
                  <span>Time:</span>
                  <strong>${appointment.time}</strong>
              </div>
          </div>

          <div class="highlight">
              <strong>📍 Location:</strong> MicroLocs Beauty Salon<br>
              <strong>⏰ Please arrive 10 minutes early</strong><br>
              <strong>📞 Need to reschedule?</strong> Call us at (*************
          </div>

          <p>To ensure the best experience, please:</p>
          <ul style="margin: 20px 0; padding-left: 20px;">
              <li>✅ Arrive with clean hair (if applicable)</li>
              <li>✅ Bring inspiration photos</li>
              <li>✅ Wear comfortable clothing</li>
              <li>✅ Turn off your phone for relaxation</li>
          </ul>

          <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/dashboard" class="button">View Appointment Details</a>
      </div>
    `;

    const template: EmailTemplate = {
      subject: `⏰ Reminder: Your appointment tomorrow at ${appointment.time}`,
      html: await this.getBaseTemplate(content, 'Appointment Reminder'),
      text: `Reminder: You have an appointment for ${appointment.service} tomorrow (${appointmentDate}) at ${appointment.time}. Please arrive 10 minutes early.`
    };

    await this.sendEmail(user.email, template);
  }

  // Marketing email for special promotions
  async sendPromotionalEmail(user: UserType, promotion: { title: string; description: string; discount: string; validUntil: string; code?: string }): Promise<void> {
    const content = `
      <div class="header">
          <h1>🎉 Special Offer Just for You!</h1>
          <p>Limited time beauty deals</p>
      </div>
      <div class="content">
          <h2>Hello ${user.firstName || user.name}!</h2>
          <p>We have something special just for you! As one of our valued clients, you get exclusive access to this amazing offer.</p>

          <div style="background: linear-gradient(135deg, #f3d016 0%, #e6c200 100%); color: white; padding: 30px; border-radius: 12px; text-align: center; margin: 25px 0;">
              <h3 style="font-size: 24px; margin-bottom: 10px;">${promotion.title}</h3>
              <p style="font-size: 18px; margin-bottom: 15px;">${promotion.description}</p>
              <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; display: inline-block;">
                  <span style="font-size: 32px; font-weight: bold;">${promotion.discount} OFF</span>
              </div>
              ${promotion.code ? `
              <p style="margin-top: 15px; font-size: 16px;">Use code: <strong style="background: rgba(255,255,255,0.3); padding: 5px 10px; border-radius: 4px;">${promotion.code}</strong></p>
              ` : ''}
          </div>

          <div class="highlight">
              <strong>⏰ Hurry! This offer expires on ${promotion.validUntil}</strong><br>
              Don't miss out on this exclusive opportunity to treat yourself!
          </div>

          <p>Popular services included in this promotion:</p>
          <ul style="margin: 20px 0; padding-left: 20px;">
              <li>💇‍♀️ Hair styling and cuts</li>
              <li>💅 Manicure and pedicure</li>
              <li>✨ Facial treatments</li>
              <li>💄 Makeup consultations</li>
          </ul>

          <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/consultation" class="button">Book Now & Save!</a>

          <p style="font-size: 14px; color: #666; margin-top: 20px;">
              *Terms and conditions apply. Cannot be combined with other offers. Valid for new bookings only.
          </p>
      </div>
    `;

    const template: EmailTemplate = {
      subject: `🎉 ${promotion.discount} OFF - Exclusive Offer for ${user.firstName || user.name}!`,
      html: await this.getBaseTemplate(content, 'Special Promotion'),
      text: `Special offer for ${user.firstName || user.name}: ${promotion.title} - ${promotion.discount} OFF! ${promotion.description} Valid until ${promotion.validUntil}. ${promotion.code ? `Use code: ${promotion.code}` : ''} Book now!`
    };

    await this.sendEmail(user.email, template);
  }

  // Birthday special email
  async sendBirthdayEmail(user: UserType): Promise<void> {
    const content = `
      <div class="header">
          <h1>🎂 Happy Birthday!</h1>
          <p>Your special day deserves special treatment</p>
      </div>
      <div class="content">
          <h2>Happy Birthday, ${user.firstName || user.name}! 🎉</h2>
          <p>It's your special day, and we want to help you celebrate in style! As our birthday gift to you, enjoy an exclusive treat.</p>

          <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; border-radius: 12px; text-align: center; margin: 25px 0;">
              <h3 style="font-size: 24px; margin-bottom: 10px;">🎁 Birthday Special</h3>
              <p style="font-size: 18px; margin-bottom: 15px;">Complimentary birthday makeover!</p>
              <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; display: inline-block;">
                  <span style="font-size: 28px; font-weight: bold;">FREE Consultation + 25% OFF</span>
              </div>
              <p style="margin-top: 15px; font-size: 16px;">Use code: <strong style="background: rgba(255,255,255,0.3); padding: 5px 10px; border-radius: 4px;">BIRTHDAY25</strong></p>
          </div>

          <p>Make your birthday unforgettable with:</p>
          <ul style="margin: 20px 0; padding-left: 20px;">
              <li>🌟 Signature birthday makeover</li>
              <li>💇‍♀️ Fresh new hairstyle</li>
              <li>💅 Birthday nail art</li>
              <li>📸 Professional photos of your new look</li>
          </ul>

          <div class="highlight">
              <strong>🎂 Valid for the entire birthday month!</strong><br>
              Book anytime this month and enjoy your special birthday treatment.
          </div>

          <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/consultation" class="button">Claim My Birthday Gift</a>

          <p>Wishing you a day filled with happiness and a year filled with joy. Thank you for being part of the MicroLocs family!</p>
      </div>
    `;

    const template: EmailTemplate = {
      subject: `🎂 Happy Birthday ${user.firstName || user.name}! Your special gift awaits`,
      html: await this.getBaseTemplate(content, 'Happy Birthday'),
      text: `Happy Birthday ${user.firstName || user.name}! Enjoy a FREE consultation + 25% OFF any service this month. Use code: BIRTHDAY25. Book your birthday makeover today!`
    };

    await this.sendEmail(user.email, template);
  }

  private async sendEmail(to: string, template: EmailTemplate): Promise<void> {
    try {
      const branding = await this.getBrandingData();
      const siteName = branding.global?.siteName || branding.business?.name || 'MicroLocs';

      const mailOptions = {
        from: `"${siteName}" <${process.env.SMTP_USER}>`,
        to,
        subject: template.subject,
        text: template.text,
        html: template.html
      };

      await this.transporter.sendMail(mailOptions);
      console.log(`Email sent successfully to ${to}`);
    } catch (error) {
      console.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }
}

export const emailService = new EmailService();
