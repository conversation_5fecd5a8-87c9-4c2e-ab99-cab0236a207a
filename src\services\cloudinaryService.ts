import { v2 as cloudinary } from 'cloudinary';
import { Request } from 'express';
import multer from 'multer';
import fs from 'fs';
import path from 'path';
import { extractPublicId } from 'cloudinary-build-url';
import dotenv from 'dotenv';

dotenv.config();

// Cloudinary configuration
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Use the Lambda-compatible temporary directory
const tmpDir = process.env.NODE_ENV === 'production' ? '/tmp' : path.resolve(__dirname, '../uploads/temp');

// Ensure tmp directory exists
if (!fs.existsSync(tmpDir)) {
  fs.mkdirSync(tmpDir, { recursive: true });
}

// Multer storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, tmpDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  },
});

// Configure multer with file filters
export const upload = multer({
  storage: storage,
  // No file size limit
  fileFilter: (req, file, cb) => {
    const allowedMimes = [
      'image/jpeg',
      'image/png',
      'image/jpg',
      'image/gif',
      'image/webp',
      'application/pdf', // Allow PDFs for documents
    ];

    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, GIF, WebP and PDF files are allowed.'));
    }
  }
});

// Upload to Cloudinary function
export const uploadToCloudinary = async (filePath: string, folder: string): Promise<string> => {
  try {
    const result = await cloudinary.uploader.upload(filePath, { 
      folder,
      resource_type: 'auto',
      allowed_formats: ['jpg', 'png', 'jpeg', 'gif', 'webp', 'pdf'],
    });
    
    // Clean up local file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    
    return result.secure_url;
  } catch (error) {
    console.error('Cloudinary upload failed:', error);
    // Clean up local file on error
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    throw new Error('File upload failed');
  }
};

// Delete from Cloudinary function
export const deleteFromCloudinary = async (imageUrl: string): Promise<boolean> => {
  try {
    const publicId = extractPublicId(imageUrl);
    if (!publicId) {
      throw new Error('Invalid image URL');
    }
    
    const result = await cloudinary.uploader.destroy(publicId);
    return result.result === 'ok';
  } catch (error) {
    console.error('Cloudinary delete failed:', error);
    return false;
  }
};

// Define upload types for different use cases
export type UploadType = 
  | 'profilePicture' 
  | 'productImage' 
  | 'serviceImage' 
  | 'brandingImage' 
  | 'testimonialImage'
  | 'staffImage'
  | 'businessDocs'
  | 'logo'
  | 'favicon'
  | 'heroImage'
  | 'galleryImage';

// Unified uploader function
export const unifiedUploader = async (req: Request, uploadType: UploadType): Promise<string[]> => {
  try {
    // Handle different types of file inputs
    const files = (() => {
      if (req.files) {
        if (Array.isArray(req.files)) return req.files;
        return Object.values(req.files).flat();
      }
      if (req.file) return [req.file];
      return [];
    })();

    if (files.length === 0) {
      console.warn('No files found in request');
      return [];
    }

    // Map upload types to Cloudinary folders
    const folderMap: Record<UploadType, string> = {
      profilePicture: 'microlocs/profile_pictures',
      productImage: 'microlocs/products',
      serviceImage: 'microlocs/services',
      brandingImage: 'microlocs/branding',
      testimonialImage: 'microlocs/testimonials',
      staffImage: 'microlocs/staff',
      businessDocs: 'microlocs/business_documents',
      logo: 'microlocs/branding/logos',
      favicon: 'microlocs/branding/favicons',
      heroImage: 'microlocs/branding/hero',
      galleryImage: 'microlocs/gallery',
    };

    const folder = folderMap[uploadType];

    // Upload files and handle errors for each file individually
    const uploadPromises = files.map(async (file) => {
      try {
        if (!file.path) {
          console.error('File path is missing:', file);
          return null;
        }

        console.log(`Processing upload: ${file.originalname} to ${folder}`);
        return await uploadToCloudinary(file.path, folder);
      } catch (error) {
        console.error(`Failed to upload ${file.originalname}:`, error);
        return null;
      }
    });

    const results = await Promise.all(uploadPromises);
    return results.filter((url): url is string => url !== null);

  } catch (error) {
    console.error('Unhandled error in unifiedUploader:', error);
    throw error;
  }
};

// Single file uploader
export const uploadSingleFile = async (req: Request, uploadType: UploadType): Promise<string | null> => {
  const results = await unifiedUploader(req, uploadType);
  return results.length > 0 ? results[0] : null;
};

// Multiple files uploader
export const uploadMultipleFiles = async (req: Request, uploadType: UploadType): Promise<string[]> => {
  return await unifiedUploader(req, uploadType);
};

// Middleware for single file upload
export const uploadSingle = (fieldName: string) => upload.single(fieldName);

// Middleware for multiple file upload
export const uploadMultiple = (fieldName: string, maxCount: number = 10) => upload.array(fieldName, maxCount);

// Middleware for multiple fields upload
export const uploadFields = (fields: { name: string; maxCount?: number }[]) => upload.fields(fields);

// Utility function to validate Cloudinary configuration
export const validateCloudinaryConfig = (): boolean => {
  const { cloud_name, api_key, api_secret } = cloudinary.config();
  return !!(cloud_name && api_key && api_secret);
};

// Get optimized image URL with transformations
export const getOptimizedImageUrl = (
  imageUrl: string, 
  options: {
    width?: number;
    height?: number;
    quality?: string;
    format?: string;
    crop?: string;
  } = {}
): string => {
  try {
    const publicId = extractPublicId(imageUrl);
    if (!publicId) return imageUrl;

    const transformations: string[] = [];
    
    if (options.width) transformations.push(`w_${options.width}`);
    if (options.height) transformations.push(`h_${options.height}`);
    if (options.quality) transformations.push(`q_${options.quality}`);
    if (options.format) transformations.push(`f_${options.format}`);
    if (options.crop) transformations.push(`c_${options.crop}`);

    const transformationString = transformations.join(',');
    
    return cloudinary.url(publicId, {
      transformation: transformationString || undefined,
      secure: true
    });
  } catch (error) {
    console.error('Error generating optimized URL:', error);
    return imageUrl;
  }
};
