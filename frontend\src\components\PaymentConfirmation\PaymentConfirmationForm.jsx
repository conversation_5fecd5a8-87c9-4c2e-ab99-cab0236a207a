import { useState, useRef } from 'react';
import { FiUpload, FiX, FiFile } from 'react-icons/fi';
import { useToast } from '../../contexts/ToastContext';

const PaymentConfirmationForm = ({
  onSubmit,
  onCancel = null,
  branding,
  orderData = null,
  appointmentData = null,
  initialData = null,
  isEditing = false
}) => {
  const { showSuccess, showError } = useToast();
  const fileInputRef = useRef(null);
  
  const [formData, setFormData] = useState({
    amount: initialData?.amount || orderData?.total || appointmentData?.price || '',
    paymentMethod: initialData?.paymentMethod || '',
    notes: initialData?.notes || '',
    proofImage: initialData?.proofImage || null
  });
  
  const [imagePreview, setImagePreview] = useState(initialData?.proofImage || null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState({});

  const validateForm = () => {
    const newErrors = {};

    if (!formData.amount || formData.amount <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    }

    if (!formData.paymentMethod.trim()) {
      newErrors.paymentMethod = 'Payment method is required';
    }

    if (!formData.proofImage) {
      newErrors.proofImage = 'Payment proof image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = async (file) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      showError('Please upload a JPG, PNG, or PDF file');
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      showError('File size must be less than 5MB');
      return;
    }

    setIsUploading(true);
    try {
      // Convert file to base64 for upload
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target.result;
        setFormData(prev => ({ ...prev, proofImage: base64 }));
        setImagePreview(base64);
        if (errors.proofImage) {
          setErrors(prev => ({ ...prev, proofImage: null }));
        }
      };
      reader.readAsDataURL(file);
    } catch (error) {
      showError('Failed to process file');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const removeImage = () => {
    setFormData(prev => ({ ...prev, proofImage: null }));
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      const submitData = {
        ...formData,
        order: orderData?._id,
        appointment: appointmentData?._id
      };

      await onSubmit(submitData);
      
      showSuccess(branding?.paymentConfirmation?.successMessage || 'Payment confirmation submitted successfully!');
      
      // Reset form if not editing
      if (!isEditing) {
        setFormData({ amount: '', paymentMethod: '', notes: '', proofImage: null });
        setImagePreview(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    } catch (error) {
      showError(`Failed to ${isEditing ? 'update' : 'submit'} payment confirmation: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isImage = (file) => {
    return file && (file.startsWith('data:image/') || file.includes('image'));
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {branding?.paymentConfirmation?.pageTitle || 'Payment Confirmation'}
        </h2>
        <p className="text-gray-600">
          {branding?.paymentConfirmation?.pageDescription || 'Please upload your payment proof to complete your order.'}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Reference Information */}
        <div className="bg-gray-50 rounded-xl p-4">
          <h3 className="font-medium text-gray-900 mb-3">Order Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {orderData && (
              <>
                <div>
                  <span className="text-gray-600">
                    {branding?.paymentConfirmation?.orderReferenceLabel || 'Order Reference'}:
                  </span>
                  <span className="ml-2 font-medium">{orderData.orderNumber}</span>
                </div>
                <div>
                  <span className="text-gray-600">Total Amount:</span>
                  <span className="ml-2 font-medium">${orderData.total}</span>
                </div>
              </>
            )}
            {appointmentData && (
              <>
                <div>
                  <span className="text-gray-600">
                    {branding?.paymentConfirmation?.appointmentReferenceLabel || 'Appointment Reference'}:
                  </span>
                  <span className="ml-2 font-medium">{appointmentData._id}</span>
                </div>
                <div>
                  <span className="text-gray-600">Service:</span>
                  <span className="ml-2 font-medium">{appointmentData.service?.name}</span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Amount */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.amountLabel || 'Payment Amount'} *
          </label>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) || '' }))}
              className={`w-full pl-8 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                errors.amount ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="0.00"
            />
          </div>
          {errors.amount && (
            <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
          )}
        </div>

        {/* Payment Method */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.paymentMethodLabel || 'Payment Method'} *
          </label>
          <input
            type="text"
            value={formData.paymentMethod}
            onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}
            className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
              errors.paymentMethod ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="e.g., Bank Transfer, PayPal, Cash App"
          />
          {errors.paymentMethod && (
            <p className="mt-1 text-sm text-red-600">{errors.paymentMethod}</p>
          )}
        </div>

        {/* File Upload */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.uploadTitle || 'Upload Payment Proof'} *
          </label>
          <p className="text-sm text-gray-600 mb-4">
            {branding?.paymentConfirmation?.uploadInstructions || 'Please upload a clear image of your payment confirmation, receipt, or transaction screenshot.'}
          </p>

          {!imagePreview ? (
            <div
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors duration-200 ${
                errors.proofImage ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
              }`}
            >
              <FiUpload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-700 mb-2">
                {branding?.paymentConfirmation?.dragDropText || 'Drag and drop your image here'}
              </p>
              <p className="text-gray-600 mb-4">
                {branding?.paymentConfirmation?.browseFilesText || 'or browse files'}
              </p>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="px-6 py-3 bg-blue-600 text-white font-medium rounded-xl hover:bg-blue-700 transition-colors duration-200"
                disabled={isUploading}
              >
                {isUploading 
                  ? (branding?.paymentConfirmation?.uploadingText || 'Uploading...')
                  : 'Choose File'
                }
              </button>
              <p className="text-xs text-gray-500 mt-2">
                {branding?.paymentConfirmation?.supportedFormats || 'Supported formats: JPG, PNG, PDF (max 5MB)'}
              </p>
            </div>
          ) : (
            <div className="border border-gray-300 rounded-xl p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium text-gray-900">
                  {branding?.paymentConfirmation?.previewTitle || 'Image Preview'}
                </h4>
                <button
                  type="button"
                  onClick={removeImage}
                  className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>
              
              <div className="flex items-center justify-center bg-gray-50 rounded-lg p-4">
                {isImage(imagePreview) ? (
                  <img
                    src={imagePreview}
                    alt="Payment proof preview"
                    className="max-w-full max-h-64 object-contain rounded-lg"
                  />
                ) : (
                  <div className="flex items-center space-x-2 text-gray-600">
                    <FiFile className="w-8 h-8" />
                    <span>PDF File Uploaded</span>
                  </div>
                )}
              </div>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,.pdf"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          {errors.proofImage && (
            <p className="mt-1 text-sm text-red-600">{errors.proofImage}</p>
          )}
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {branding?.paymentConfirmation?.notesLabel || 'Additional Notes'}
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder={branding?.paymentConfirmation?.notesPlaceholder || 'Any additional information about your payment...'}
            rows={3}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none"
            maxLength={500}
          />
          <p className="text-xs text-gray-500 mt-1">
            {formData.notes.length}/500
          </p>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <div className="flex items-center justify-end space-x-4">
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                className="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors duration-200"
                disabled={isSubmitting || isUploading}
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              disabled={isSubmitting || isUploading}
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting
                ? (branding?.paymentConfirmation?.submittingText || 'Submitting...')
                : (branding?.paymentConfirmation?.submitButton || 'Submit Payment Proof')
              }
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default PaymentConfirmationForm;
