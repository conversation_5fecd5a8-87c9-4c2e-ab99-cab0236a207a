import { useState } from 'react'
import { FiChevronDown, FiChevronUp, FiCalendar, FiClock, FiUser, FiPhone, FiMail } from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'

const MultiDayAppointments = ({ dashboardStats, onNavigateToTab, isLoading = false }) => {
  const { branding } = useBranding()
  const [expandedSections, setExpandedSections] = useState({
    today: true,
    tomorrow: false,
    week: false
  })

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const formatTime = (timeString) => {
    if (!timeString) return 'Time not set'

    // Handle different time formats
    if (timeString.includes(':')) {
      const [hours, minutes] = timeString.split(':')
      const hour = parseInt(hours)
      const ampm = hour >= 12 ? 'PM' : 'AM'
      const displayHour = hour % 12 || 12
      return `${displayHour}:${minutes} ${ampm}`
    }

    return timeString
  }

  // Helper function to format duration from minutes to readable format
  const formatDuration = (minutes) => {
    if (!minutes) return 'Duration not set'
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60

    if (hours === 0) {
      return `${mins} min${mins !== 1 ? 's' : ''}`
    } else if (mins === 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''}`
    } else {
      return `${hours}h ${mins}m`
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Date not set'
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    })
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const AppointmentCard = ({ appointment }) => (
    <div className="p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-4 flex-1">
          <div className="w-12 h-12 rounded-full flex items-center justify-center"
               style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}20, ${branding.colors.accent}20)` }}>
            <FiUser className="w-6 h-6" style={{ color: branding.colors.secondary }} />
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-semibold text-gray-900">
                {appointment.customerInfo?.name || appointment.user?.name || 'Unknown Customer'}
              </h4>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                {appointment.status || 'Pending'}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {appointment.service?.name || appointment.service || 'Service'}
            </p>
            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <div className="flex items-center space-x-1">
                <FiClock className="w-3 h-3" />
                <span>{formatTime(appointment.time)}</span>
              </div>
              {appointment.service?.duration && (
                <div className="flex items-center space-x-1">
                  <FiCalendar className="w-3 h-3" />
                  <span>{formatDuration(appointment.service.duration)}</span>
                </div>
              )}
              {appointment.customerInfo?.phone && (
                <div className="flex items-center space-x-1">
                  <FiPhone className="w-3 h-3" />
                  <span>{appointment.customerInfo.phone}</span>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="text-right">
          <p className="font-semibold text-gray-900">
            ${appointment.service?.price || '0.00'}
          </p>
          {appointment.customerInfo?.email && (
            <div className="flex items-center space-x-1 text-xs text-gray-500 mt-1">
              <FiMail className="w-3 h-3" />
              <span className="truncate max-w-24">{appointment.customerInfo.email}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  const AppointmentSection = ({ title, count, appointments, sectionKey, subtitle }) => (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
      <div
        className="p-6 cursor-pointer hover:bg-gray-50/50 transition-colors duration-200"
        onClick={() => toggleSection(sectionKey)}
      >
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-3">
              <h3 className="text-xl font-bold text-gray-900">{title}</h3>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                {isLoading ? (
                  <div className="h-4 bg-blue-300 rounded w-6 animate-pulse"></div>
                ) : (
                  count
                )}
              </span>
            </div>
            <p className="text-gray-600 mt-1">{subtitle}</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onNavigateToTab('appointments')
              }}
              className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02] text-sm"
            >
              View All
            </button>
            {expandedSections[sectionKey] ? (
              <FiChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <FiChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </div>
        </div>
      </div>
      
      {expandedSections[sectionKey] && (
        <div className="px-6 pb-6">
          {appointments && appointments.length > 0 ? (
            <div className="space-y-3">
              {appointments.map((appointment, index) => (
                <AppointmentCard key={appointment._id || index} appointment={appointment} />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                <FiCalendar className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-500 font-medium">No appointments scheduled</p>
              <p className="text-gray-400 text-sm mt-1">
                {sectionKey === 'today' ? 'Your schedule is clear for today' : 
                 sectionKey === 'tomorrow' ? 'No appointments scheduled for tomorrow' :
                 'No appointments scheduled for this week'}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )

  return (
    <div className="space-y-6">
      <AppointmentSection
        title="Today's Appointments"
        count={dashboardStats?.today?.appointments || 0}
        appointments={dashboardStats?.appointments?.today || []}
        sectionKey="today"
        subtitle="Manage your schedule for today"
      />
      
      <AppointmentSection
        title="Tomorrow's Appointments"
        count={dashboardStats?.tomorrow?.appointments || 0}
        appointments={dashboardStats?.appointments?.tomorrow || []}
        sectionKey="tomorrow"
        subtitle="Prepare for tomorrow's schedule"
      />
      
      <AppointmentSection
        title="This Week's Appointments"
        count={dashboardStats?.week?.appointments || 0}
        appointments={dashboardStats?.appointments?.week || []}
        sectionKey="week"
        subtitle="Overview of your weekly schedule"
      />
    </div>
  )
}

export default MultiDayAppointments
