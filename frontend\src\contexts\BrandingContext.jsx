import { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { brandingService } from '../services/brandingService'

// No hardcoded fallbacks - all data comes from API only
const BrandingContext = createContext({
  branding: null,
  isLoading: true,
  error: null,
  refreshBranding: () => {},
  updateBranding: () => {}
})

export const BrandingProvider = ({ children }) => {
  const [branding, setBranding] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [pollInterval, setPollInterval] = useState(null)

  // Load branding data from API with caching - NO FALLBACKS
  const loadBranding = useCallback(async (forceRefresh = false) => {
    try {
      setError(null)

      const response = await brandingService.getBranding(forceRefresh)

      if (response.fromCache) {
        console.log('BrandingContext: Using cached data')
      } else {
        console.log('BrandingContext: Loaded fresh data from API')
      }

      if (response.success && response.data) {
        // The /api/branding endpoint returns the complete nested structure
        const { global, home, services, shop, consultation, login, signup, cart, productDetail, footer, dashboard, buttons, navigation, testimonials, reviews, messages, business, theme, site, legal } = response.data

        // Use ONLY API data - no fallbacks or defaults
        const apiBranding = {
          // Business info from API only
          businessName: business?.name || '',
          tagline: business?.tagline || '',
          description: business?.description || '',
          phone: business?.phone || '',
          email: business?.email || '',
          address: business?.address || '',
          social: business?.social || {},
          hours: business?.hours || {},

          // Theme from API only
          colors: theme?.colors || {},
          fonts: theme?.fonts || {},

          // Branding assets from API only
          images: {
            logo: global?.logo || '',
            hero: home?.heroImage || '',
            favicon: global?.favicon || ''
          },

          // Content from API only - using nested structure
          content: {
            // Global content
            siteName: global?.siteName || '',
            tagline: global?.tagline || '',
            phone: global?.phone || '',
            email: global?.email || '',
            address: global?.address || '',

            // Home page content
            heroTitle: home?.heroTitle || '',
            heroSubtitle: home?.heroSubtitle || '',
            aboutTitle: home?.aboutTitle || '',
            aboutText: home?.aboutText || '',

            // Services content
            servicesTitle: services?.pageTitle || '',
            servicesSubtitle: services?.pageSubtitle || '',
            servicesDescription: services?.pageDescription || '',

            // Shop content
            shopTitle: shop?.pageTitle || '',
            shopSubtitle: shop?.pageSubtitle || '',
            shopDescription: shop?.pageDescription || '',

            // Consultation content
            consultationTitle: consultation?.pageTitle || '',
            consultationSubtitle: consultation?.pageSubtitle || '',
            consultationDescription: consultation?.pageDescription || '',

            // Authentication content
            loginTitle: response.data.loginTitle || '',
            loginSubtitle: response.data.loginSubtitle || '',
            signInButton: response.data.signInButton || 'Sign In',
            signingInText: response.data.signingInText || 'Signing in...',
            noAccountText: response.data.noAccountText || "Don't have an account?",
            signUpLink: response.data.signUpLink || 'Sign up here',
            forgotPasswordLink: response.data.forgotPasswordLink || 'Forgot your password?',
            signupTitle: response.data.signupTitle || '',
            signupSubtitle: response.data.signupSubtitle || '',
            createAccountButton: response.data.createAccountButton || 'Create Account',
            creatingAccountText: response.data.creatingAccountText || 'Creating account...',
            haveAccountText: response.data.haveAccountText || 'Already have an account?',
            signInLink: response.data.signInLink || 'Sign in here',

            // Dashboard content
            dashboardWelcome: dashboard?.welcomeMessage || '',
            dashboardOverviewTitle: dashboard?.overviewTitle || '',
            dashboardAppointmentsTitle: dashboard?.appointmentsTitle || '',
            dashboardOrdersTitle: dashboard?.ordersTitle || '',
            dashboardFavoritesTitle: dashboard?.favoritesTitle || '',
            dashboardProfileTitle: dashboard?.profileTitle || '',

            // Buttons
            buttons: buttons || {},

            // Navigation
            navigation: navigation || {},

            // Footer
            footer: footer || {},

            // Messages
            messages: messages || {},

            // Testimonials
            testimonials: testimonials || {},

            // Reviews
            reviews: reviews || {}
          },

          // Site settings from API only
          seo: site?.seo || {},
          features: site?.features || {},

          // Legal content from API only
          legal: legal || {}
        }

        setBranding(apiBranding)
      } else {
        throw new Error('No branding data received from API')
      }
    } catch (error) {
      console.error('Error loading branding:', error)
      setError('Failed to load branding configuration')
      // Do NOT use fallbacks - leave branding as null
      setBranding(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load branding on mount and set up moderate polling for real-time updates
  useEffect(() => {
    loadBranding(false) // Use cache on initial load

    // Set up moderate polling every 5 minutes for real-time updates
    // Since we have 30min cache, we can poll less frequently
    const interval = setInterval(() => {
      loadBranding(false) // Use cache for polling too
    }, 300000) // 5 minutes

    setPollInterval(interval)

    return () => {
      if (interval) {
        clearInterval(interval)
      }
    }
  }, [loadBranding])

  // Also listen for window focus to refresh branding
  useEffect(() => {
    const handleFocus = () => {
      loadBranding(false) // Use cache on focus
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [loadBranding])

  // Listen for localStorage changes to trigger immediate updates
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'branding_updated') {
        console.log('Branding update detected, refreshing...')
        loadBranding()
        // Clear the flag
        localStorage.removeItem('branding_updated')
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // Also check for changes in the same tab more frequently for immediate updates
    const checkForUpdates = () => {
      if (localStorage.getItem('branding_updated')) {
        console.log('Branding update detected (same tab), refreshing...')
        loadBranding()
        localStorage.removeItem('branding_updated')
      }
    }

    const updateCheckInterval = setInterval(checkForUpdates, 500) // Check every 500ms for immediate updates

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      clearInterval(updateCheckInterval)
    }
  }, [loadBranding])

  // Refresh branding data manually (force refresh)
  const refreshBranding = useCallback(() => {
    loadBranding(true) // Force refresh from API
  }, [loadBranding])

  // Update branding (for admin use) and refresh immediately
  const updateBranding = useCallback((newBranding) => {
    setBranding(prev => ({ ...prev, ...newBranding }))
    // Clear cache and refresh from API to get latest data
    brandingService.clearCache()
    setTimeout(() => {
      loadBranding(true) // Force refresh
    }, 1000)
  }, [loadBranding])

  const value = {
    branding,
    isLoading,
    error,
    refreshBranding,
    updateBranding
  }

  return (
    <BrandingContext.Provider value={value}>
      {children}
    </BrandingContext.Provider>
  )
}

// Custom hook to use branding context
export const useBranding = () => {
  const context = useContext(BrandingContext)
  if (!context) {
    throw new Error('useBranding must be used within a BrandingProvider')
  }
  return context
}

export default BrandingContext
