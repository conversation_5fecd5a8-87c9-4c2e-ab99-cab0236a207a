"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const services_1 = require("../services");
const response_1 = require("../utils/response");
class AuthController {
    static async register(req, res) {
        try {
            const { firstName, lastName, email, phone, password } = req.body;
            const result = await services_1.AuthService.register({
                firstName,
                lastName,
                name: `${firstName} ${lastName}`.trim(),
                email,
                phone,
                password
            });
            (0, response_1.sendCreated)(res, 'User registered successfully', {
                user: result.user,
                token: result.token,
                refreshToken: result.refreshToken
            });
        }
        catch (error) {
            console.error('Registration error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async login(req, res) {
        try {
            const { email, password, rememberMe } = req.body;
            const result = await services_1.AuthService.login(email, password);
            (0, response_1.sendSuccess)(res, 'Login successful', {
                user: result.user,
                token: result.token,
                refreshToken: result.refreshToken,
                rememberMe: rememberMe || false
            });
        }
        catch (error) {
            console.error('Login error:', error);
            (0, response_1.sendError)(res, error.message, undefined, 401);
        }
    }
    static async forgotPassword(req, res) {
        try {
            const { email } = req.body;
            await services_1.AuthService.forgotPassword(email);
            (0, response_1.sendSuccess)(res, 'Password reset instructions sent to your email');
        }
        catch (error) {
            console.error('Forgot password error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async resetPassword(req, res) {
        try {
            const { token, password } = req.body;
            await services_1.AuthService.resetPassword(token, password);
            (0, response_1.sendSuccess)(res, 'Password reset successful');
        }
        catch (error) {
            console.error('Reset password error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    static async verify(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'User not found', undefined, 401);
                return;
            }
            const user = await services_1.AuthService.verifyToken(req.user._id);
            (0, response_1.sendSuccess)(res, 'Token verified successfully', { user });
        }
        catch (error) {
            console.error('Token verification error:', error);
            (0, response_1.sendError)(res, error.message, undefined, 401);
        }
    }
    static async logout(req, res) {
        try {
            if (!req.user) {
                (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
                return;
            }
            // Extract token from Authorization header
            const authHeader = req.headers.authorization;
            if (authHeader && authHeader.startsWith('Bearer ')) {
                const token = authHeader.substring(7);
                // Add token to blacklist (implement token blacklist service)
                await services_1.AuthService.blacklistToken(token);
            }
            (0, response_1.sendSuccess)(res, 'Logout successful');
        }
        catch (error) {
            console.error('Logout error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.AuthController = AuthController;
