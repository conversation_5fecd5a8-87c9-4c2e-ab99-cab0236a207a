import { Request, Response } from 'express';
import { PaymentConfirmationService } from '../services/paymentConfirmationService';
import { sendSuccess, sendError, sendCreated } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class PaymentConfirmationController {
  /**
   * Create a new payment confirmation
   */
  static async createPaymentConfirmation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { order, appointment, amount, paymentMethod, proofImage, notes } = req.body;

      const confirmation = await PaymentConfirmationService.createPaymentConfirmation({
        user: req.user._id,
        order,
        appointment,
        amount,
        paymentMethod,
        proofImage,
        notes
      });

      sendCreated(res, 'Payment confirmation submitted successfully', confirmation);
    } catch (error) {
      console.error('Create payment confirmation error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Get user's own payment confirmations
   */
  static async getUserPaymentConfirmations(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { page, limit } = req.query;

      const result = await PaymentConfirmationService.getUserPaymentConfirmations(
        req.user._id,
        page ? parseInt(page as string) : undefined,
        limit ? parseInt(limit as string) : undefined
      );

      sendSuccess(res, 'Payment confirmations retrieved successfully', result);
    } catch (error) {
      console.error('Get user payment confirmations error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Get payment confirmation by ID
   */
  static async getPaymentConfirmationById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      const confirmation = await PaymentConfirmationService.getPaymentConfirmationById(id);

      if (!confirmation) {
        sendError(res, 'Payment confirmation not found', undefined, 404);
        return;
      }

      // Users can only view their own confirmations, admins can view all
      if (req.user.role !== 'admin' && confirmation.user.toString() !== req.user._id) {
        sendError(res, 'Access denied', undefined, 403);
        return;
      }

      sendSuccess(res, 'Payment confirmation retrieved successfully', confirmation);
    } catch (error) {
      console.error('Get payment confirmation error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Update user's own payment confirmation
   */
  static async updatePaymentConfirmation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;
      const { amount, paymentMethod, notes, proofImage } = req.body;

      const confirmation = await PaymentConfirmationService.updatePaymentConfirmation(
        id,
        req.user._id,
        { amount, paymentMethod, notes, proofImage }
      );

      if (!confirmation) {
        sendError(res, 'Payment confirmation not found or cannot be edited', undefined, 404);
        return;
      }

      sendSuccess(res, 'Payment confirmation updated successfully', confirmation);
    } catch (error) {
      console.error('Update payment confirmation error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Delete user's own payment confirmation
   */
  static async deletePaymentConfirmation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        sendError(res, 'Authentication required', undefined, 401);
        return;
      }

      const { id } = req.params;

      const deleted = await PaymentConfirmationService.deletePaymentConfirmation(id, req.user._id);

      if (!deleted) {
        sendError(res, 'Payment confirmation not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Payment confirmation deleted successfully');
    } catch (error) {
      console.error('Delete payment confirmation error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Admin: Get all payment confirmations
   */
  static async getAllPaymentConfirmations(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        sendError(res, 'Admin access required', undefined, 403);
        return;
      }

      const { status, page, limit, search } = req.query;

      const result = await PaymentConfirmationService.getAllPaymentConfirmations({
        status: status as string,
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
        search: search as string
      });

      sendSuccess(res, 'All payment confirmations retrieved successfully', result);
    } catch (error) {
      console.error('Get all payment confirmations error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Admin: Update payment confirmation status
   */
  static async updatePaymentConfirmationStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        sendError(res, 'Admin access required', undefined, 403);
        return;
      }

      const { id } = req.params;
      const { status, rejectionReason } = req.body;

      if (!['verified', 'rejected'].includes(status)) {
        sendError(res, 'Invalid status. Must be "verified" or "rejected"', undefined, 400);
        return;
      }

      const confirmation = await PaymentConfirmationService.updatePaymentConfirmationStatus(
        id,
        status,
        req.user._id,
        rejectionReason
      );

      if (!confirmation) {
        sendError(res, 'Payment confirmation not found', undefined, 404);
        return;
      }

      sendSuccess(res, `Payment confirmation ${status} successfully`, confirmation);
    } catch (error) {
      console.error('Update payment confirmation status error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Admin: Delete any payment confirmation
   */
  static async adminDeletePaymentConfirmation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        sendError(res, 'Admin access required', undefined, 403);
        return;
      }

      const { id } = req.params;

      const deleted = await PaymentConfirmationService.deletePaymentConfirmation(id);

      if (!deleted) {
        sendError(res, 'Payment confirmation not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Payment confirmation deleted successfully');
    } catch (error) {
      console.error('Admin delete payment confirmation error:', error);
      sendError(res, (error as Error).message);
    }
  }

  /**
   * Admin: Get payment confirmation statistics
   */
  static async getPaymentConfirmationStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user || req.user.role !== 'admin') {
        sendError(res, 'Admin access required', undefined, 403);
        return;
      }

      const stats = await PaymentConfirmationService.getPaymentConfirmationStats();

      sendSuccess(res, 'Payment confirmation statistics retrieved successfully', stats);
    } catch (error) {
      console.error('Get payment confirmation stats error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
