import { Request, Response } from 'express';
import { User, Appointment, Order, Product, Service } from '../models';
import { sendSuccess, sendError, sendCreated } from '../utils/response';
import { AdminQuery, AuthenticatedRequest } from '../types';
import { AppointmentService } from '../services';

export class AdminController {
  static async getDashboardStats(req: Request, res: Response): Promise<void> {
    try {
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
      const startOfTomorrow = new Date(tomorrow.getFullYear(), tomorrow.getMonth(), tomorrow.getDate());
      const endOfTomorrow = new Date(startOfTomorrow.getTime() + 24 * 60 * 60 * 1000);

      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      startOfWeek.setHours(0, 0, 0, 0);
      const endOfWeek = new Date(startOfWeek.getTime() + 7 * 24 * 60 * 60 * 1000);

      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const startOfYear = new Date(today.getFullYear(), 0, 1);

      const [
        totalUsers,
        totalAppointments,
        totalOrders,
        totalProducts,
        totalServices,
        todayAppointments,
        tomorrowAppointments,
        weekAppointments,
        todayOrders,
        monthlyRevenue,
        lastMonthRevenue,
        yearlyRevenue,
        pendingAppointments,
        processingOrders,
        newCustomersThisMonth,
        newCustomersLastMonth,
        // Get detailed appointments for today, tomorrow, and this week
        todayAppointmentsList,
        tomorrowAppointmentsList,
        weekAppointmentsList
      ] = await Promise.all([
        User.countDocuments({ role: 'user' }),
        Appointment.countDocuments(),
        Order.countDocuments(),
        Product.countDocuments({ isActive: true }),
        Service.countDocuments({ isActive: true }),
        Appointment.countDocuments({
          date: { $gte: startOfDay, $lt: endOfDay }
        }),
        Appointment.countDocuments({
          date: { $gte: startOfTomorrow, $lt: endOfTomorrow }
        }),
        Appointment.countDocuments({
          date: { $gte: startOfWeek, $lt: endOfWeek }
        }),
        Order.countDocuments({
          createdAt: { $gte: startOfDay }
        }),
        Order.aggregate([
          { $match: {
            createdAt: { $gte: startOfMonth },
            paymentStatus: 'paid'
          }},
          { $group: { _id: null, total: { $sum: '$totalAmount' } }}
        ]),
        Order.aggregate([
          { $match: {
            createdAt: { $gte: startOfLastMonth, $lt: startOfMonth },
            paymentStatus: 'paid'
          }},
          { $group: { _id: null, total: { $sum: '$totalAmount' } }}
        ]),
        Order.aggregate([
          { $match: {
            createdAt: { $gte: startOfYear },
            paymentStatus: 'paid'
          }},
          { $group: { _id: null, total: { $sum: '$totalAmount' } }}
        ]),
        Appointment.countDocuments({ status: 'pending' }),
        Order.countDocuments({ status: 'processing' }),
        User.countDocuments({
          role: 'user',
          createdAt: { $gte: startOfMonth }
        }),
        User.countDocuments({
          role: 'user',
          createdAt: { $gte: startOfLastMonth, $lt: startOfMonth }
        }),
        // Detailed appointments lists
        Appointment.find({
          date: { $gte: startOfDay, $lt: endOfDay }
        })
          .populate('user', 'name email phone')
          .populate('service', 'name duration price')
          .sort({ time: 1 })
          .limit(10),
        Appointment.find({
          date: { $gte: startOfTomorrow, $lt: endOfTomorrow }
        })
          .populate('user', 'name email phone')
          .populate('service', 'name duration price')
          .sort({ time: 1 })
          .limit(10),
        Appointment.find({
          date: { $gte: startOfWeek, $lt: endOfWeek }
        })
          .populate('user', 'name email phone')
          .populate('service', 'name duration price')
          .sort({ date: 1, time: 1 })
          .limit(20)
      ]);

      // Calculate growth percentages
      const revenueGrowth = lastMonthRevenue[0]?.total
        ? Math.round(((monthlyRevenue[0]?.total || 0) - lastMonthRevenue[0].total) / lastMonthRevenue[0].total * 100)
        : 0;

      const customerGrowth = newCustomersLastMonth
        ? Math.round((newCustomersThisMonth - newCustomersLastMonth) / newCustomersLastMonth * 100)
        : 0;

      const stats = {
        overview: {
          totalUsers,
          totalAppointments,
          totalOrders,
          totalProducts,
          totalServices
        },
        today: {
          appointments: todayAppointments,
          orders: todayOrders
        },
        tomorrow: {
          appointments: tomorrowAppointments
        },
        week: {
          appointments: weekAppointments
        },
        revenue: {
          monthly: monthlyRevenue[0]?.total || 0,
          lastMonth: lastMonthRevenue[0]?.total || 0,
          yearly: yearlyRevenue[0]?.total || 0,
          growth: revenueGrowth
        },
        customers: {
          newThisMonth: newCustomersThisMonth,
          newLastMonth: newCustomersLastMonth,
          growth: customerGrowth
        },
        pending: {
          appointments: pendingAppointments,
          orders: processingOrders
        },
        appointments: {
          today: todayAppointmentsList,
          tomorrow: tomorrowAppointmentsList,
          week: weekAppointmentsList
        }
      };

      sendSuccess(res, 'Dashboard statistics retrieved successfully', stats);
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getAppointments(req: Request, res: Response): Promise<void> {
    try {
      const {
        status,
        date,
        page = 1,
        limit = 20,
        search
      } = req.query as AdminQuery;

      const filter: any = {};

      if (status) {
        filter.status = status;
      }

      if (date) {
        const searchDate = new Date(date);
        filter.date = {
          $gte: searchDate,
          $lt: new Date(searchDate.getTime() + 24 * 60 * 60 * 1000)
        };
      }

      if (search) {
        filter.$or = [
          { 'customerInfo.name': { $regex: search, $options: 'i' } },
          { 'customerInfo.email': { $regex: search, $options: 'i' } },
          { 'customerInfo.phone': { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [appointments, total] = await Promise.all([
        Appointment.find(filter)
          .populate('user', 'name email phone')
          .populate('service', 'name duration price')
          .sort({ date: -1, time: -1 })
          .skip(skip)
          .limit(limitNum),
        Appointment.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Appointments retrieved successfully', {
        appointments,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin appointments error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createAppointment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { service, date, time, firstName, lastName, email, phone, message, userId } = req.body;

      let finalUserId = userId;

      // If userId is 'temp-user', create a temporary user or find existing user by email
      if (userId === 'temp-user') {
        // First, try to find existing user by email
        let existingUser = await User.findOne({ email: email.toLowerCase() });

        if (!existingUser) {
          // Create a new user account
          existingUser = new User({
            name: `${firstName} ${lastName}`.trim(),
            email: email.toLowerCase(),
            phone,
            role: 'user',
            password: 'temp-password-' + Date.now(), // Temporary password
            isEmailVerified: false
          });
          await existingUser.save();
        }

        finalUserId = existingUser._id;
      }

      const appointmentData: any = {
        user: finalUserId,
        service,
        date: new Date(date),
        time,
        customerInfo: {
          name: `${firstName} ${lastName}`.trim(),
          email,
          phone
        },
        message,
        type: 'service'
      };

      const appointment = await AppointmentService.createAppointment(appointmentData);

      sendCreated(res, 'Appointment created successfully', appointment);
    } catch (error) {
      console.error('Create admin appointment error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateAppointment(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const appointment = await Appointment.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).populate('user', 'name email phone')
       .populate('service', 'name duration price');

      if (!appointment) {
        sendError(res, 'Appointment not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Appointment updated successfully', appointment);
    } catch (error) {
      console.error('Update appointment error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteAppointment(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const appointment = await Appointment.findByIdAndDelete(id);

      if (!appointment) {
        sendError(res, 'Appointment not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Appointment deleted successfully');
    } catch (error) {
      console.error('Delete appointment error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getCustomers(req: Request, res: Response): Promise<void> {
    try {
      const {
        search,
        page = 1,
        limit = 20
      } = req.query as AdminQuery;

      const filter: any = { role: 'user' };

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { phone: { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [customers, total] = await Promise.all([
        User.find(filter)
          .select('-password')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        User.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Customers retrieved successfully', {
        customers,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin customers error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async createCustomer(req: Request, res: Response): Promise<void> {
    try {
      const { name, email, phone, password } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({ email: email.toLowerCase() });
      if (existingUser) {
        sendError(res, 'User with this email already exists', undefined, 400);
        return;
      }

      const customer = await User.create({
        name,
        email: email.toLowerCase(),
        phone,
        password: password || 'temp-password-' + Date.now(),
        role: 'user',
        isEmailVerified: false
      });

      // Remove password from response
      const customerResponse = customer.toObject();
      delete (customerResponse as any).password;

      sendCreated(res, 'Customer created successfully', customerResponse);
    } catch (error) {
      console.error('Create customer error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateCustomer(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Remove password from update data if it's empty
      if (updateData.password === '') {
        delete updateData.password;
      }

      const customer = await User.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).select('-password');

      if (!customer) {
        sendError(res, 'Customer not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Customer updated successfully', customer);
    } catch (error) {
      console.error('Update customer error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteCustomer(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const customer = await User.findByIdAndDelete(id);

      if (!customer) {
        sendError(res, 'Customer not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Customer deleted successfully');
    } catch (error) {
      console.error('Delete customer error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getOrders(req: Request, res: Response): Promise<void> {
    try {
      const {
        status,
        page = 1,
        limit = 20,
        search
      } = req.query as AdminQuery;

      const filter: any = {};

      if (status) {
        filter.status = status;
      }

      if (search) {
        filter.$or = [
          { orderNumber: { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [orders, total] = await Promise.all([
        Order.find(filter)
          .populate('user', 'name email phone')
          .populate('items.product', 'name images')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Order.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Orders retrieved successfully', {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin orders error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async updateOrder(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const order = await Order.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).populate('user', 'name email phone')
       .populate('items.product', 'name images');

      if (!order) {
        sendError(res, 'Order not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Order updated successfully', order);
    } catch (error) {
      console.error('Update order error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async deleteOrder(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const order = await Order.findByIdAndDelete(id);

      if (!order) {
        sendError(res, 'Order not found', undefined, 404);
        return;
      }

      sendSuccess(res, 'Order deleted successfully');
    } catch (error) {
      console.error('Delete order error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getProducts(req: Request, res: Response): Promise<void> {
    try {
      const {
        category,
        page = 1,
        limit = 20,
        search
      } = req.query as AdminQuery;

      const filter: any = {};

      if (category) {
        filter.category = category;
      }

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [products, total] = await Promise.all([
        Product.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Product.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Products retrieved successfully', {
        products,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin products error:', error);
      sendError(res, (error as Error).message);
    }
  }

  static async getServices(req: Request, res: Response): Promise<void> {
    try {
      const {
        category,
        page = 1,
        limit = 20,
        search,
        isActive
      } = req.query as AdminQuery;

      const filter: any = {};

      if (category) {
        filter.category = category;
      }

      if (search) {
        filter.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      if (isActive !== undefined) {
        filter.isActive = isActive === 'true';
      }

      const pageNum = Number(page);
      const limitNum = Number(limit);
      const skip = (pageNum - 1) * limitNum;

      const [services, total] = await Promise.all([
        Service.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limitNum),
        Service.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limitNum);

      sendSuccess(res, 'Services retrieved successfully', {
        services,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalItems: total,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1
        }
      });
    } catch (error) {
      console.error('Get admin services error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
