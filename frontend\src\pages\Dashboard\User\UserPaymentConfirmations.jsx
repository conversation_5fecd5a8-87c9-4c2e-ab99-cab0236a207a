import { useState, useEffect, useCallback, useRef } from 'react';
import { FiDollarSign, FiPlus, FiEye, FiEdit3, FiTrash2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>he<PERSON>, FiX } from 'react-icons/fi';
import { useApiDataFetching } from '../../../hooks/useDataFetching';
import { useBranding } from '../../../contexts/BrandingContext';
import { useToast } from '../../../contexts/ToastContext';
import { PaymentConfirmationForm, PaymentConfirmationList } from '../../../components/PaymentConfirmation';
import { apiService } from '../../../services';
import Loading from '../../../components/Loading';

const UserPaymentConfirmations = () => {
  const { branding } = useBranding();
  const { showSuccess, showError } = useToast();
  const loadingRef = useRef(false);

  const [confirmations, setConfirmations] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingConfirmation, setEditingConfirmation] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);

  // Memoized function to load payment confirmations data
  const loadConfirmationsData = useCallback(async () => {
    if (loadingRef.current) return;

    try {
      loadingRef.current = true;

      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10'
      });

      const response = await apiService.get(`/payment-confirmations/my?${params}`);

      if (response.success) {
        return {
          confirmations: response.data.confirmations || [],
          total: response.data.total || 0,
          hasMore: response.data.hasMore || false
        };
      } else {
        throw new Error('Failed to load payment confirmations');
      }
    } catch (error) {
      console.error('Error loading payment confirmations:', error);
      throw error;
    } finally {
      loadingRef.current = false;
    }
  }, [page]);

  // Use the custom hook for data loading
  const {
    data: confirmationsData,
    loading,
    error,
    refetch
  } = useApiDataFetching(
    loadConfirmationsData,
    [page]
  );

  useEffect(() => {
    if (confirmationsData) {
      if (page === 1) {
        setConfirmations(confirmationsData.confirmations || []);
      } else {
        setConfirmations(prev => [...prev, ...(confirmationsData.confirmations || [])]);
      }
      setHasMore(confirmationsData.hasMore || false);
    }
  }, [confirmationsData, page]);

  const handleSubmitConfirmation = async (confirmationData) => {
    try {
      const response = await apiService.post('/payment-confirmations', confirmationData);
      
      if (response.success) {
        setShowForm(false);
        setPage(1);
        refetch();
        showSuccess('Payment confirmation submitted successfully!');
      } else {
        throw new Error(response.message || 'Failed to submit payment confirmation');
      }
    } catch (error) {
      throw error; // Let the form handle the error
    }
  };

  const handleUpdateConfirmation = async (confirmationData) => {
    try {
      const response = await apiService.put(`/payment-confirmations/${editingConfirmation._id}`, confirmationData);
      
      if (response.success) {
        setEditingConfirmation(null);
        setPage(1);
        refetch();
        showSuccess('Payment confirmation updated successfully!');
      } else {
        throw new Error(response.message || 'Failed to update payment confirmation');
      }
    } catch (error) {
      throw error; // Let the form handle the error
    }
  };

  const handleDeleteConfirmation = async (confirmationId) => {
    try {
      const response = await apiService.delete(`/payment-confirmations/${confirmationId}`);
      
      if (response.success) {
        setPage(1);
        refetch();
        showSuccess('Payment confirmation deleted successfully');
      } else {
        throw new Error(response.message || 'Failed to delete payment confirmation');
      }
    } catch (error) {
      showError(`Failed to delete payment confirmation: ${error.message}`);
    }
  };

  const loadMore = () => {
    if (hasMore && !loading) {
      setPage(prev => prev + 1);
    }
  };

  const getStatusStats = () => {
    const stats = { pending: 0, verified: 0, rejected: 0 };
    confirmations.forEach(confirmation => {
      stats[confirmation.status]++;
    });
    return stats;
  };

  const stats = getStatusStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Confirmations</h1>
          <p className="text-gray-600">Manage your payment confirmations and track verification status</p>
        </div>
        
        {!showForm && !editingConfirmation && (
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            Submit Payment Proof
          </button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <FiClock className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <FiCheck className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Verified</p>
              <p className="text-2xl font-bold text-gray-900">{stats.verified}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
              <FiX className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">{stats.rejected}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Confirmation Form */}
      {(showForm || editingConfirmation) && (
        <PaymentConfirmationForm
          onSubmit={editingConfirmation ? handleUpdateConfirmation : handleSubmitConfirmation}
          branding={branding}
          initialData={editingConfirmation}
          isEditing={!!editingConfirmation}
          onCancel={() => {
            setShowForm(false);
            setEditingConfirmation(null);
          }}
        />
      )}

      {/* Payment Confirmations List */}
      {!showForm && !editingConfirmation && (
        <>
          <PaymentConfirmationList
            confirmations={confirmations}
            branding={branding}
            onEdit={setEditingConfirmation}
            onDelete={handleDeleteConfirmation}
            showActions={true}
            isAdmin={false}
            loading={loading && page === 1}
          />

          {/* Load More Button */}
          {hasMore && !loading && (
            <div className="text-center">
              <button
                onClick={loadMore}
                className="px-6 py-3 bg-white/80 backdrop-blur-sm text-gray-700 font-medium rounded-xl hover:bg-white/90 focus:ring-4 focus:ring-blue-300 transition-all duration-200 shadow-lg border border-white/20"
              >
                Load More
              </button>
            </div>
          )}

          {/* Loading indicator for pagination */}
          {loading && page > 1 && (
            <div className="text-center py-4">
              <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
          )}
        </>
      )}

      {/* Empty State */}
      {!loading && confirmations.length === 0 && !showForm && !editingConfirmation && (
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20 text-center">
          <FiDollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Payment Confirmations
          </h3>
          <p className="text-gray-600 mb-6">
            You haven't submitted any payment confirmations yet.
          </p>
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-xl hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transition-all duration-200"
          >
            <FiPlus className="w-4 h-4 mr-2" />
            Submit Your First Payment Proof
          </button>
        </div>
      )}
    </div>
  );
};

export default UserPaymentConfirmations;
