import { Router } from 'express';
import { UploadController } from '../controllers';
import { authenticate } from '../middleware/auth';

const router = Router();

// ===== UNIFIED CLOUDINARY UPLOAD ROUTES =====

// Check Cloudinary configuration
router.get('/cloudinary/config', UploadController.checkCloudinaryConfig);

// Single file upload with type parameter
// POST /api/upload/cloudinary/single/:uploadType
router.post(
  '/cloudinary/single/:uploadType',
  authenticate,
  UploadController.cloudinaryUploadSingle('file'),
  UploadController.uploadSingleFileToCloudinary
);

// Multiple files upload with type parameter
// POST /api/upload/cloudinary/multiple/:uploadType
router.post(
  '/cloudinary/multiple/:uploadType',
  authenticate,
  UploadController.cloudinaryUploadMultiple('files', 10),
  UploadController.uploadMultipleFilesToCloudinary
);

// DELETE /api/upload/cloudinary/image - Delete image from Cloudinary
router.delete(
  '/cloudinary/image',
  authenticate,
  UploadController.deleteImageFromCloudinary
);

// GET /api/upload/cloudinary/optimize/:imageUrl - Get optimized image URL
router.get(
  '/cloudinary/optimize/:imageUrl',
  UploadController.getOptimizedImage
);

// ===== LEGACY LOCAL ROUTES (for backward compatibility) =====

// POST /api/upload/image
router.post(
  '/image',
  authenticate,
  UploadController.uploadMiddleware,
  UploadController.uploadImage
);

// POST /api/upload/images
router.post(
  '/images',
  authenticate,
  UploadController.uploadMultipleImages
);

// GET /api/upload/image/:filename
router.get(
  '/image/:filename',
  UploadController.getImageInfo
);

// DELETE /api/upload/image/:filename
router.delete(
  '/image/:filename',
  authenticate,
  UploadController.deleteImage
);

export default router;
