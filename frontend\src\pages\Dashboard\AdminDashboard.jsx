import { useState, useEffect, useCallback, useRef } from 'react'
import {
  FiUsers,
  FiCalendar,
  FiShoppingBag,
  FiBarChart,
  FiPackage,
  FiEdit,
  FiTool,
  FiStar,
  FiDollarSign
} from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'
import { useRouter } from '../../hooks/useRouter'
import { useAdminStats, useAdminAppointments, useAdminCustomers, useAdminOrders, useAdminProducts, useAdminServices } from '../../hooks/useQueries'
import { adminService } from '../../services'
import Loading from '../../components/Loading'

// Import new component structure
import DashboardLayout from './Shared/DashboardLayout'
import AdminOverview from './Admin/AdminOverview'
import AdminAppointments from './Admin/AdminAppointments'
import AdminCustomers from './Admin/AdminCustomers'
import AdminOrders from './Admin/AdminOrders'
import AdminProducts from './Admin/AdminProducts'
import AdminServices from './Admin/AdminServices'
import AdminBranding from './Admin/AdminBranding'
import AdminReviews from './Admin/AdminReviews'
import AdminPaymentConfirmations from './Admin/AdminPaymentConfirmations'
import ConfirmDialog from '../../components/Modals/ConfirmDialog'

const AdminDashboard = ({ onNavigate, onLogout, userProfile }) => {
  const { branding } = useBranding()
  const { parseCurrentRoute, navigateToSubRoute } = useRouter()

  // Use React Query hooks for data fetching
  const { data: dashboardStats, isLoading: statsLoading } = useAdminStats()
  const { data: appointmentsData, isLoading: appointmentsLoading } = useAdminAppointments()
  const { data: customersData, isLoading: customersLoading } = useAdminCustomers()
  const { data: ordersData, isLoading: ordersLoading } = useAdminOrders()
  const { data: productsData, isLoading: productsLoading } = useAdminProducts()
  const { data: servicesData, isLoading: servicesLoading } = useAdminServices()

  // Check if user has admin role
  useEffect(() => {
    if (userProfile && userProfile.role !== 'admin') {
      console.log('User does not have admin role, redirecting to user dashboard')
      onNavigate('user-dashboard')
      return
    }
  }, [userProfile, onNavigate])

  // Initialize activeTab from URL if available
  const [activeTab, setActiveTab] = useState(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    return (mainRoute === 'admin-dashboard' && subRoute) ? subRoute : 'overview'
  })

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [viewingItem, setViewingItem] = useState(null)
  const [toast, setToast] = useState(null)
  const [confirmDialog, setConfirmDialog] = useState(null)
  const [modalType, setModalType] = useState('') // 'add', 'edit', 'view'

  const [error, setError] = useState('')

  // Create a sectionLoading object from React Query loading states
  const sectionLoading = {
    overview: statsLoading,
    appointments: appointmentsLoading,
    customers: customersLoading,
    orders: ordersLoading,
    products: productsLoading,
    services: servicesLoading,
    dashboardStats: statsLoading
  }

  // Overall loading state
  const isLoading = statsLoading || appointmentsLoading || customersLoading ||
                   ordersLoading || productsLoading || servicesLoading














  // Data loading is now handled by React Query hooks automatically

  // Update URL when tab changes
  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    navigateToSubRoute('admin-dashboard', tabId, { scrollToTop: true })
    // React Query will handle data loading automatically
  }

  // Listen to route changes (browser back/forward) and update activeTab
  useEffect(() => {
    const { subRoute } = parseCurrentRoute()
    if (subRoute && subRoute !== activeTab) {
      setActiveTab(subRoute)
    }
  }, [parseCurrentRoute, activeTab])

  // Set initial URL if no sub-route is present
  useEffect(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    if (mainRoute === 'admin-dashboard' && !subRoute) {
      navigateToSubRoute('admin-dashboard', 'overview', { replace: true, scrollToTop: false })
    }
  }, [parseCurrentRoute, navigateToSubRoute])



  // Toast notification helper
  const showToast = (message, type = 'success') => {
    setToast({ message, type })
    setTimeout(() => setToast(null), 3000)
  }

  // Confirmation dialog helper
  const showConfirmDialog = (title, message, onConfirm) => {
    setConfirmDialog({ title, message, onConfirm })
  }

  // Refresh data function for components that need it
  const refreshData = () => {
    // React Query will automatically refetch data when needed
    // This is a placeholder for compatibility
  }

  // CRUD Handlers for different entities
  const handleDeleteAppointment = async (appointmentId) => {
    try {
      await adminService.deleteAppointment(appointmentId)
      showToast('Appointment deleted successfully!', 'success')
      // React Query will automatically refetch the data
    } catch (error) {
      console.error('Delete appointment error:', error)
      showToast('Failed to delete appointment. Please try again.', 'error')
    }
  }

  const handleDeleteCustomer = async (customerId) => {
    try {
      await adminService.deleteCustomer(customerId)
      showToast('Customer deleted successfully!', 'success')
      // React Query will automatically refetch the data
    } catch (error) {
      console.error('Delete customer error:', error)
      showToast('Failed to delete customer. Please try again.', 'error')
    }
  }

  const handleDeleteOrder = async (orderId) => {
    try {
      await adminService.deleteOrder(orderId)
      showToast('Order deleted successfully!', 'success')
      // React Query will automatically refetch the data
    } catch (error) {
      console.error('Delete order error:', error)
      showToast('Failed to delete order. Please try again.', 'error')
    }
  }

  const handleDeleteProduct = async (productId) => {
    try {
      await adminService.deleteProduct(productId)
      showToast('Product deleted successfully!', 'success')
      // React Query will automatically refetch the data
    } catch (error) {
      console.error('Delete product error:', error)
      showToast('Failed to delete product. Please try again.', 'error')
    }
  }

  const handleDeleteService = async (serviceId) => {
    try {
      await adminService.deleteService(serviceId)
      showToast('Service deleted successfully!', 'success')
      // React Query will automatically refetch the data
    } catch (error) {
      console.error('Delete service error:', error)
      showToast('Failed to delete service. Please try again.', 'error')
    }
  }

  if (isLoading) {
    return <Loading />
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiBarChart },
    { id: 'appointments', label: 'Appointments', icon: FiCalendar },
    { id: 'customers', label: 'Customers', icon: FiUsers },
    { id: 'orders', label: 'Orders', icon: FiShoppingBag },
    { id: 'products', label: 'Products', icon: FiPackage },
    { id: 'services', label: 'Services', icon: FiTool },
    { id: 'reviews', label: 'Reviews', icon: FiStar },
    { id: 'payment-confirmations', label: 'Payment Confirmations', icon: FiDollarSign },
    { id: 'branding', label: 'Branding', icon: FiEdit }
  ]

  // Render the appropriate component based on activeTab
  const renderActiveComponent = () => {
    const commonProps = {
      searchTerm,
      setSearchTerm,
      statusFilter,
      setStatusFilter,
      showAddModal,
      setShowAddModal,
      editingItem,
      setEditingItem,
      viewingItem,
      setViewingItem,
      modalType,
      setModalType,
      showToast,
      showConfirmDialog,
      sectionLoading,
      branding,
      adminData: userProfile,
      handleDeleteAppointment,
      handleDeleteCustomer,
      handleDeleteOrder,
      handleDeleteProduct,
      handleDeleteService,
      refreshData
    }

    switch (activeTab) {
      case 'overview':
        return (
          <AdminOverview
            dashboardStats={dashboardStats}
            sectionLoading={sectionLoading}
            adminData={userProfile}
            onNavigateToTab={handleTabChange}
          />
        )
      case 'appointments':
        console.log('Appointments page data:', appointmentsData)
        return (
          <AdminAppointments
            {...commonProps}
            appointments={appointmentsData?.appointments || []}
            services={servicesData?.services || []}
            customers={customersData?.customers || []}
          />
        )
      case 'customers':
        console.log('Customers page data:', customersData)
        return (
          <AdminCustomers
            {...commonProps}
            customers={customersData?.customers || []}
          />
        )
      case 'orders':
        console.log('Orders page data:', ordersData)
        return (
          <AdminOrders
            {...commonProps}
            orders={ordersData?.orders || []}
          />
        )
      case 'products':
        console.log('Products page data:', productsData)
        return (
          <AdminProducts
            {...commonProps}
            products={productsData?.products || []}
          />
        )
      case 'services':
        console.log('Services page data:', servicesData)
        return (
          <AdminServices
            {...commonProps}
            services={servicesData?.services || []}
          />
        )
      case 'reviews':
        return <AdminReviews />
      case 'payment-confirmations':
        return <AdminPaymentConfirmations />
      case 'branding':
        return (
          <AdminBranding
            showToast={showToast}
            branding={branding}
          />
        )
      default:
        return (
          <AdminOverview
            dashboardStats={dashboardStats}
            sectionLoading={sectionLoading}
            adminData={userProfile}
            onNavigateToTab={handleTabChange}
          />
        )
    }
  }

  return (
    <DashboardLayout
      userType="admin"
      activeTab={activeTab}
      setActiveTab={handleTabChange}
      userData={userProfile}
      onLogout={onLogout}
      sidebarItems={tabs}
    >
      {renderActiveComponent()}

      {/* Confirmation Dialog */}
      {confirmDialog && (
        <ConfirmDialog
          isOpen={!!confirmDialog}
          title={confirmDialog.title}
          message={confirmDialog.message}
          onConfirm={confirmDialog.onConfirm}
          onCancel={() => setConfirmDialog(null)}
          confirmText="Delete"
          cancelText="Cancel"
          type="danger"
        />
      )}

      {/* Toast Notification */}
      {toast && (
        <div className="fixed top-4 right-4 z-50">
          <div className={`px-6 py-4 rounded-lg shadow-lg ${
            toast.type === 'success'
              ? 'bg-green-500 text-white'
              : 'bg-red-500 text-white'
          }`}>
            {toast.message}
          </div>
        </div>
      )}
    </DashboardLayout>
  )
}

export default AdminDashboard
