import apiService from './api.js'

class BrandingService {
  constructor() {
    this.CACHE_KEY = 'branding_data_cache'
    this.CACHE_DURATION = 30 * 60 * 1000 // 30 minutes in milliseconds
  }

  /**
   * Get cached branding data from localStorage
   */
  getCachedBranding() {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY)
      if (!cached) return null

      const { data, timestamp } = JSON.parse(cached)
      const now = Date.now()

      // Check if cache is still valid (within 30 minutes)
      if (now - timestamp < this.CACHE_DURATION) {
        console.log('Using cached branding data')
        return data
      } else {
        // Cache expired, remove it
        localStorage.removeItem(this.CACHE_KEY)
        console.log('Branding cache expired, removed')
        return null
      }
    } catch (error) {
      console.error('Error reading branding cache:', error)
      localStorage.removeItem(this.CACHE_KEY)
      return null
    }
  }

  /**
   * Cache branding data in localStorage
   */
  cacheBranding(data) {
    try {
      const cacheData = {
        data,
        timestamp: Date.now()
      }
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheData))
      console.log('Branding data cached for 30 minutes')
    } catch (error) {
      console.error('Error caching branding data:', error)
    }
  }

  /**
   * Clear branding cache
   */
  clearCache() {
    try {
      localStorage.removeItem(this.CACHE_KEY)
      console.log('Branding cache cleared')
    } catch (error) {
      console.error('Error clearing branding cache:', error)
    }
  }

  /**
   * Get branding configuration from API with caching
   */
  async getBranding(forceRefresh = false) {
    try {
      // Check cache first unless force refresh is requested
      if (!forceRefresh) {
        const cachedData = this.getCachedBranding()
        if (cachedData) {
          return {
            success: true,
            data: cachedData,
            fromCache: true
          }
        }
      }

      console.log('Fetching fresh branding data from API...')
      const response = await apiService.get('/branding')

      if (response.success && response.data) {
        // Cache the successful response
        this.cacheBranding(response.data)
        return {
          ...response,
          fromCache: false
        }
      }

      return response
    } catch (error) {
      console.error('Error fetching branding:', error)
      return {
        success: false,
        message: 'Failed to fetch branding data',
        error: error.message
      }
    }
  }

  /**
   * Get business profile information
   */
  async getBusinessProfile() {
    try {
      const response = await apiService.get('/business-profile')
      return response
    } catch (error) {
      console.error('Error fetching business profile:', error)
      return {
        success: false,
        message: 'Failed to fetch business profile',
        error: error.message
      }
    }
  }

  /**
   * Get theme settings
   */
  async getThemeSettings() {
    try {
      const response = await apiService.get('/theme-settings')
      return response
    } catch (error) {
      console.error('Error fetching theme settings:', error)
      return {
        success: false,
        message: 'Failed to fetch theme settings',
        error: error.message
      }
    }
  }

  /**
   * Get site settings
   */
  async getSiteSettings() {
    try {
      const response = await apiService.get('/site-settings')
      return response
    } catch (error) {
      console.error('Error fetching site settings:', error)
      return {
        success: false,
        message: 'Failed to fetch site settings',
        error: error.message
      }
    }
  }

  /**
   * Get complete branding configuration from single aggregated endpoint
   * This replaces multiple API calls with one efficient request
   */
  async getCompleteBranding() {
    try {
      // Try the new aggregated endpoint first
      const response = await apiService.get('/branding/complete?includeAuth=false')

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data
        }
      } else {
        throw new Error('Invalid response format from aggregated endpoint')
      }
    } catch (error) {
      console.warn('Aggregated branding endpoint failed, falling back to individual requests:', error.message)

      // Fallback: use individual endpoints if aggregated endpoint fails
      try {
        const [
          brandingResponse,
          businessResponse,
          themeResponse,
          siteResponse
        ] = await Promise.all([
          this.getBranding(),
          this.getBusinessProfile(),
          this.getThemeSettings(),
          this.getSiteSettings()
        ])

        return {
          success: true,
          data: {
            branding: brandingResponse.success ? brandingResponse.data : null,
            business: businessResponse.success ? businessResponse.data : null,
            theme: themeResponse.success ? themeResponse.data : null,
            site: siteResponse.success ? siteResponse.data : null
          }
        }
      } catch (fallbackError) {
        console.error('Both aggregated and individual branding requests failed:', fallbackError)
        return {
          success: false,
          message: 'Failed to fetch complete branding data',
          error: fallbackError.message
        }
      }
    }
  }

  /**
   * Update branding content (admin only)
   */
  async updateBranding(brandingData) {
    try {
      const response = await apiService.put('/branding', brandingData)

      // Clear cache when data is updated so fresh data is fetched next time
      if (response.success) {
        this.clearCache()
        console.log('Branding updated successfully, cache cleared')
      }

      return response
    } catch (error) {
      console.error('Error updating branding:', error)
      return {
        success: false,
        message: 'Failed to update branding data',
        error: error.message
      }
    }
  }

  /**
   * Update specific branding section (admin only)
   */
  async updateBrandingSection(section, sectionData) {
    try {
      const response = await apiService.put(`/branding/${section}`, sectionData)

      // Clear cache when data is updated so fresh data is fetched next time
      if (response.success) {
        this.clearCache()
        console.log(`Branding section '${section}' updated successfully, cache cleared`)
      }

      return response
    } catch (error) {
      console.error('Error updating branding section:', error)
      return {
        success: false,
        message: 'Failed to update branding section',
        error: error.message
      }
    }
  }
}

export const brandingService = new BrandingService()
