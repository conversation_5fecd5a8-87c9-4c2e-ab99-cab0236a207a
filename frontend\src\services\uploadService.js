import apiService from './api.js'

/**
 * Upload service for handling file uploads
 */
class UploadService {
  /**
   * Upload single image
   */
  async uploadImage(file, options = {}) {
    try {
      const response = await apiService.upload('/upload/image', file, options)
      return response
    } catch (error) {
      console.error('Upload image error:', error)
      throw error
    }
  }

  /**
   * Upload multiple images
   */
  async uploadImages(files, options = {}) {
    try {
      const formData = new FormData()
      
      // Add files to form data
      if (Array.isArray(files)) {
        files.forEach((file, index) => {
          formData.append(`files`, file)
        })
      } else {
        formData.append('files', files)
      }

      // Add additional options
      Object.keys(options).forEach(key => {
        formData.append(key, options[key])
      })

      const response = await apiService.request('/upload/images', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getAuthToken()}`
          // Don't set Content-Type for FormData, let browser set it
        },
        body: formData
      })

      return response
    } catch (error) {
      console.error('Upload images error:', error)
      throw error
    }
  }

  /**
   * Get image info
   */
  async getImageInfo(filename) {
    try {
      const response = await apiService.get(`/upload/image/${filename}`)
      return response
    } catch (error) {
      console.error('Get image info error:', error)
      throw error
    }
  }

  /**
   * Delete image
   */
  async deleteImage(filename) {
    try {
      const response = await apiService.delete(`/upload/image/${filename}`)
      return response
    } catch (error) {
      console.error('Delete image error:', error)
      throw error
    }
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(file) {
    try {
      const response = await apiService.upload('/users/profile/picture', file)
      return response
    } catch (error) {
      console.error('Upload profile picture error:', error)
      throw error
    }
  }

  /**
   * Upload product image
   */
  async uploadProductImage(file, productId) {
    try {
      const response = await apiService.upload('/upload/image', file, {
        type: 'product',
        productId
      })
      return response
    } catch (error) {
      console.error('Upload product image error:', error)
      throw error
    }
  }

  /**
   * Upload service image
   */
  async uploadServiceImage(file, serviceId) {
    try {
      const response = await apiService.upload('/upload/image', file, {
        type: 'service',
        serviceId
      })
      return response
    } catch (error) {
      console.error('Upload service image error:', error)
      throw error
    }
  }

  /**
   * Upload branding image (legacy)
   */
  async uploadBrandingImage(file, section) {
    try {
      const response = await apiService.upload('/upload/image', file, {
        type: 'branding',
        section
      })
      return response
    } catch (error) {
      console.error('Upload branding image error:', error)
      throw error
    }
  }

  // ===== CLOUDINARY UPLOAD METHODS =====

  /**
   * Check Cloudinary configuration
   */
  async checkCloudinaryConfig() {
    try {
      const response = await apiService.get('/upload/cloudinary/config')
      return response
    } catch (error) {
      console.error('Check Cloudinary config error:', error)
      throw error
    }
  }

  /**
   * Upload single file to Cloudinary using unified route
   */
  async uploadFileToCloudinary(file, uploadType = 'galleryImage') {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await apiService.request(`/upload/cloudinary/single/${uploadType}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getAuthToken()}`
        },
        body: formData
      })
      return response
    } catch (error) {
      console.error('Upload file to Cloudinary error:', error)
      throw error
    }
  }

  /**
   * Upload multiple files to Cloudinary using unified route
   */
  async uploadFilesToCloudinary(files, uploadType = 'galleryImage') {
    try {
      const formData = new FormData()

      if (Array.isArray(files)) {
        files.forEach(file => formData.append('files', file))
      } else {
        formData.append('files', files)
      }

      const response = await apiService.request(`/upload/cloudinary/multiple/${uploadType}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getAuthToken()}`
        },
        body: formData
      })
      return response
    } catch (error) {
      console.error('Upload files to Cloudinary error:', error)
      throw error
    }
  }

  // Legacy method names for backward compatibility
  async uploadImageToCloudinary(file, uploadType = 'galleryImage') {
    return this.uploadFileToCloudinary(file, uploadType)
  }

  async uploadImagesToCloudinary(files, uploadType = 'galleryImage') {
    return this.uploadFilesToCloudinary(files, uploadType)
  }

  /**
   * Delete image from Cloudinary
   */
  async deleteImageFromCloudinary(imageUrl) {
    try {
      const response = await apiService.delete('/upload/cloudinary/image', {
        imageUrl
      })
      return response
    } catch (error) {
      console.error('Delete image from Cloudinary error:', error)
      throw error
    }
  }

  /**
   * Get optimized image URL from Cloudinary
   */
  async getOptimizedImageUrl(imageUrl, options = {}) {
    try {
      const params = new URLSearchParams(options).toString()
      const encodedUrl = encodeURIComponent(imageUrl)
      const response = await apiService.get(`/upload/cloudinary/optimize/${encodedUrl}?${params}`)
      return response
    } catch (error) {
      console.error('Get optimized image URL error:', error)
      throw error
    }
  }

  // ===== SPECIALIZED CLOUDINARY UPLOADS =====

  // ===== SPECIALIZED UPLOAD METHODS (using unified routes) =====

  /**
   * Upload profile picture to Cloudinary
   */
  async uploadProfilePictureToCloudinary(file) {
    return this.uploadFileToCloudinary(file, 'profilePicture')
  }

  /**
   * Upload product images to Cloudinary
   */
  async uploadProductImagesToCloudinary(files) {
    return this.uploadFilesToCloudinary(files, 'productImage')
  }

  /**
   * Upload branding images to Cloudinary
   */
  async uploadBrandingImagesToCloudinary(files) {
    return this.uploadFilesToCloudinary(files, 'brandingImage')
  }

  /**
   * Upload logo to Cloudinary
   */
  async uploadLogoToCloudinary(file) {
    return this.uploadFileToCloudinary(file, 'logo')
  }

  /**
   * Upload favicon to Cloudinary
   */
  async uploadFaviconToCloudinary(file) {
    return this.uploadFileToCloudinary(file, 'favicon')
  }

  /**
   * Upload hero image to Cloudinary
   */
  async uploadHeroImageToCloudinary(file) {
    return this.uploadFileToCloudinary(file, 'heroImage')
  }

  /**
   * Upload service image to Cloudinary
   */
  async uploadServiceImageToCloudinary(file) {
    return this.uploadFileToCloudinary(file, 'serviceImage')
  }

  /**
   * Upload testimonial image to Cloudinary
   */
  async uploadTestimonialImageToCloudinary(file) {
    return this.uploadFileToCloudinary(file, 'testimonialImage')
  }

  /**
   * Upload staff image to Cloudinary
   */
  async uploadStaffImageToCloudinary(file) {
    return this.uploadFileToCloudinary(file, 'staffImage')
  }

  /**
   * Upload business documents to Cloudinary
   */
  async uploadBusinessDocsToCloudinary(files) {
    return this.uploadFilesToCloudinary(files, 'businessDocs')
  }

  /**
   * Validate file before upload
   */
  validateFile(file, options = {}) {
    const {
      maxSize = 5 * 1024 * 1024, // 5MB default
      allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      maxWidth = 2048,
      maxHeight = 2048
    } = options

    const errors = []

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`)
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      errors.push(`File type must be one of: ${allowedTypes.join(', ')}`)
    }

    // Check image dimensions (if it's an image)
    if (file.type.startsWith('image/')) {
      return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => {
          if (img.width > maxWidth || img.height > maxHeight) {
            errors.push(`Image dimensions must be less than ${maxWidth}x${maxHeight}px`)
          }
          resolve({
            valid: errors.length === 0,
            errors,
            dimensions: { width: img.width, height: img.height }
          })
        }
        img.onerror = () => {
          errors.push('Invalid image file')
          resolve({
            valid: false,
            errors
          })
        }
        img.src = URL.createObjectURL(file)
      })
    }

    return Promise.resolve({
      valid: errors.length === 0,
      errors
    })
  }

  /**
   * Resize image before upload
   */
  async resizeImage(file, maxWidth = 1024, maxHeight = 1024, quality = 0.8) {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width
            width = maxWidth
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height
            height = maxHeight
          }
        }

        // Set canvas dimensions
        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height)
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              // Create new file with same name and type
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now()
              })
              resolve(resizedFile)
            } else {
              reject(new Error('Failed to resize image'))
            }
          },
          file.type,
          quality
        )
      }

      img.onerror = () => {
        reject(new Error('Failed to load image for resizing'))
      }

      img.src = URL.createObjectURL(file)
    })
  }

  /**
   * Get upload progress (for large files)
   */
  uploadWithProgress(file, endpoint, onProgress) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest()
      const formData = new FormData()
      formData.append('file', file)

      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const percentComplete = (event.loaded / event.total) * 100
          onProgress(percentComplete)
        }
      })

      // Handle completion
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText)
            resolve(response)
          } catch (error) {
            reject(new Error('Invalid response format'))
          }
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`))
        }
      })

      // Handle errors
      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'))
      })

      // Set headers
      const token = apiService.getAuthToken()
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`)
      }

      // Start upload
      xhr.open('POST', `${apiService.baseURL}${endpoint}`)
      xhr.send(formData)
    })
  }
}

// Create and export singleton instance
const uploadService = new UploadService()
export default uploadService
