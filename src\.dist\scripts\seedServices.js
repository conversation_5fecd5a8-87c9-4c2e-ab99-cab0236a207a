"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedServices = seedServices;
const mongoose_1 = __importDefault(require("mongoose"));
const Service_1 = require("../models/Service");
const dotenv_1 = __importDefault(require("dotenv"));
// Load environment variables
dotenv_1.default.config();
const services = [
    {
        name: 'Hair Cut & Style',
        description: 'Professional hair cutting and styling services tailored to your face shape and personal style. Includes wash, cut, and blow dry.',
        category: 'Hair Cut & Style',
        duration: 60, // 1 hour
        price: 75,
        isActive: true,
        image: ''
    },
    {
        name: 'Hair Color - Full Color',
        description: 'Complete hair color transformation with professional color products. Includes color consultation, application, and styling.',
        category: 'Hair Color',
        duration: 180, // 3 hours
        price: 150,
        isActive: true,
        image: ''
    },
    {
        name: 'Hair Color - Highlights',
        description: 'Professional highlighting service to add dimension and brightness to your hair. Includes foil highlights and toning.',
        category: 'Hair Color',
        duration: 120, // 2 hours
        price: 120,
        isActive: true,
        image: ''
    },
    {
        name: 'Hair Color - Root Touch Up',
        description: 'Quick root touch-up service to maintain your existing hair color. Perfect for regular maintenance.',
        category: 'Hair Color',
        duration: 90, // 1.5 hours
        price: 80,
        isActive: true,
        image: ''
    },
    {
        name: 'Deep Conditioning Treatment',
        description: 'Intensive deep conditioning treatment to restore moisture and repair damaged hair. Includes scalp massage.',
        category: 'Hair Treatment',
        duration: 45,
        price: 50,
        isActive: true,
        image: ''
    },
    {
        name: 'Keratin Treatment',
        description: 'Professional keratin treatment to smooth frizz and add shine. Results last 3-4 months.',
        category: 'Hair Treatment',
        duration: 150, // 2.5 hours
        price: 200,
        isActive: true,
        image: ''
    },
    {
        name: 'Scalp Treatment',
        description: 'Therapeutic scalp treatment to promote healthy hair growth and address scalp concerns.',
        category: 'Hair Treatment',
        duration: 60,
        price: 65,
        isActive: true,
        image: ''
    },
    {
        name: 'Box Braids',
        description: 'Traditional box braids using high-quality synthetic or human hair. Protective styling that lasts 6-8 weeks.',
        category: 'Braiding',
        duration: 240, // 4 hours
        price: 180,
        isActive: true,
        image: ''
    },
    {
        name: 'Cornrows',
        description: 'Classic cornrow braiding in various patterns and styles. Great for protective styling.',
        category: 'Braiding',
        duration: 120, // 2 hours
        price: 100,
        isActive: true,
        image: ''
    },
    {
        name: 'Twist Braids',
        description: 'Two-strand twist braids for a natural protective style. Can be worn for 4-6 weeks.',
        category: 'Braiding',
        duration: 180, // 3 hours
        price: 140,
        isActive: true,
        image: ''
    },
    {
        name: 'Sew-In Hair Extensions',
        description: 'Professional sew-in hair extensions for length and volume. Includes consultation and styling.',
        category: 'Extensions',
        duration: 180, // 3 hours
        price: 250,
        isActive: true,
        image: ''
    },
    {
        name: 'Clip-In Extensions Application',
        description: 'Professional application and styling of clip-in hair extensions. Perfect for special events.',
        category: 'Extensions',
        duration: 60,
        price: 75,
        isActive: true,
        image: ''
    },
    {
        name: 'Tape-In Extensions',
        description: 'Semi-permanent tape-in hair extensions for natural-looking length and volume.',
        category: 'Extensions',
        duration: 120, // 2 hours
        price: 200,
        isActive: true,
        image: ''
    },
    {
        name: 'Hair Consultation',
        description: 'Comprehensive hair consultation to discuss your hair goals, assess hair condition, and create a personalized treatment plan.',
        category: 'Consultation',
        duration: 30,
        price: 25,
        isActive: true,
        image: ''
    },
    {
        name: 'Color Consultation',
        description: 'Detailed color consultation to determine the best color options for your skin tone and lifestyle.',
        category: 'Consultation',
        duration: 30,
        price: 25,
        isActive: true,
        image: ''
    }
];
async function seedServices() {
    try {
        // Connect to MongoDB
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocs';
        await mongoose_1.default.connect(mongoUri);
        console.log('Connected to MongoDB');
        // Clear existing services (optional - comment out if you want to keep existing services)
        // await Service.deleteMany({});
        // console.log('Cleared existing services');
        // Insert new services
        for (const serviceData of services) {
            // Check if service already exists
            const existingService = await Service_1.Service.findOne({
                name: serviceData.name,
                category: serviceData.category
            });
            if (!existingService) {
                await Service_1.Service.create(serviceData);
                console.log(`Created service: ${serviceData.name}`);
            }
            else {
                console.log(`Service already exists: ${serviceData.name}`);
            }
        }
        console.log('Service seeding completed successfully!');
        // Display all services
        const allServices = await Service_1.Service.find({}).sort({ category: 1, name: 1 });
        console.log('\nAll services in database:');
        allServices.forEach(service => {
            console.log(`- ${service.category}: ${service.name} ($${service.price}, ${service.duration}min)`);
        });
    }
    catch (error) {
        console.error('Error seeding services:', error);
    }
    finally {
        await mongoose_1.default.disconnect();
        console.log('Disconnected from MongoDB');
    }
}
// Run the seeder
if (require.main === module) {
    seedServices();
}
