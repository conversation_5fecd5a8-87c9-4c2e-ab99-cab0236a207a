import { useState, useEffect, useCallback, useRef } from 'react';
import { FiDollarSign, FiCheck, FiX, FiSearch, FiFilter, FiEye, FiTrash2, FiClock, FiExternalLink } from 'react-icons/fi';
import { useApiDataFetching } from '../../../hooks/useDataFetching';
import { useBranding } from '../../../contexts/BrandingContext';
import { useToast } from '../../../contexts/ToastContext';
import { apiService } from '../../../services';
import Loading from '../../../components/Loading';

const AdminPaymentConfirmations = () => {
  const { branding } = useBranding();
  const { showSuccess, showError } = useToast();
  const loadingRef = useRef(false);

  const [confirmations, setConfirmations] = useState([]);
  const [selectedConfirmation, setSelectedConfirmation] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [filters, setFilters] = useState({
    status: 'all',
    search: '',
    page: 1
  });
  const [stats, setStats] = useState({
    pending: { count: 0, totalAmount: 0 },
    verified: { count: 0, totalAmount: 0 },
    rejected: { count: 0, totalAmount: 0 }
  });

  // Memoized function to load payment confirmations data
  const loadConfirmationsData = useCallback(async () => {
    if (loadingRef.current) return;

    try {
      loadingRef.current = true;

      const params = new URLSearchParams({
        page: filters.page.toString(),
        limit: '20'
      });

      if (filters.status !== 'all') {
        params.append('status', filters.status);
      }

      if (filters.search) {
        params.append('search', filters.search);
      }

      const [confirmationsResponse, statsResponse] = await Promise.all([
        apiService.get(`/payment-confirmations/admin/all?${params}`),
        apiService.get('/payment-confirmations/admin/stats')
      ]);

      const result = {};

      if (confirmationsResponse.success) {
        result.confirmations = confirmationsResponse.data.confirmations || [];
        result.total = confirmationsResponse.data.total || 0;
        result.hasMore = confirmationsResponse.data.hasMore || false;
      }

      if (statsResponse.success) {
        result.stats = statsResponse.data || {
          pending: { count: 0, totalAmount: 0 },
          verified: { count: 0, totalAmount: 0 },
          rejected: { count: 0, totalAmount: 0 }
        };
      }

      return result;
    } catch (error) {
      console.error('Error loading payment confirmations:', error);
      throw error;
    } finally {
      loadingRef.current = false;
    }
  }, [filters]);

  // Use the custom hook for data loading
  const {
    data: confirmationsData,
    loading,
    error,
    refetch
  } = useApiDataFetching(
    loadConfirmationsData,
    [filters]
  );

  useEffect(() => {
    if (confirmationsData) {
      setConfirmations(confirmationsData.confirmations || []);
      if (confirmationsData.stats) {
        setStats(confirmationsData.stats);
      }
    }
  }, [confirmationsData]);

  const handleStatusUpdate = async (confirmationId, status, rejectionReason = '') => {
    try {
      const response = await apiService.put(`/payment-confirmations/admin/${confirmationId}/status`, {
        status,
        rejectionReason
      });
      
      if (response.success) {
        showSuccess(`Payment confirmation ${status} successfully`);
        refetch();
        setSelectedConfirmation(null);
      } else {
        throw new Error(response.message || `Failed to ${status} payment confirmation`);
      }
    } catch (error) {
      showError(`Failed to ${status} payment confirmation: ${error.message}`);
    }
  };

  const handleDeleteConfirmation = async (confirmationId) => {
    if (!window.confirm('Are you sure you want to delete this payment confirmation? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await apiService.delete(`/payment-confirmations/admin/${confirmationId}`);
      
      if (response.success) {
        showSuccess('Payment confirmation deleted successfully');
        refetch();
        setSelectedConfirmation(null);
      } else {
        throw new Error(response.message || 'Failed to delete payment confirmation');
      }
    } catch (error) {
      showError(`Failed to delete payment confirmation: ${error.message}`);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setFilters(prev => ({ ...prev, page: 1 }));
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'verified':
        return <FiCheck className="w-4 h-4" />;
      case 'rejected':
        return <FiX className="w-4 h-4" />;
      case 'pending':
      default:
        return <FiClock className="w-4 h-4" />;
    }
  };

  if (loading && confirmations.length === 0) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Payment Confirmations</h1>
          <p className="text-gray-600">Manage customer payment confirmations</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
              <FiClock className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">{stats.pending.count}</p>
              <p className="text-sm text-gray-600">${stats.pending.totalAmount.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
              <FiCheck className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Verified</p>
              <p className="text-2xl font-bold text-gray-900">{stats.verified.count}</p>
              <p className="text-sm text-gray-600">${stats.verified.totalAmount.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
              <FiX className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">Rejected</p>
              <p className="text-2xl font-bold text-gray-900">{stats.rejected.count}</p>
              <p className="text-sm text-gray-600">${stats.rejected.totalAmount.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search by payment method, notes..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <FiFilter className="w-4 h-4 text-gray-500" />
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value, page: 1 }))}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="verified">Verified</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>

            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors duration-200"
            >
              Search
            </button>
          </div>
        </form>
      </div>

      {/* Confirmations List */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        {confirmations.length === 0 ? (
          <div className="p-8 text-center">
            <FiDollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment Confirmations Found</h3>
            <p className="text-gray-600">No payment confirmations match your current filters.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment Method
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order/Appointment
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {confirmations.map((confirmation) => (
                  <tr key={confirmation._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div>
                        <p className="font-medium text-gray-900">
                          {confirmation.user?.firstName} {confirmation.user?.lastName}
                        </p>
                        <p className="text-sm text-gray-600">{confirmation.user?.email}</p>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <p className="text-lg font-bold text-gray-900">
                        ${confirmation.amount.toFixed(2)}
                      </p>
                    </td>
                    <td className="px-6 py-4">
                      <p className="text-sm text-gray-900">{confirmation.paymentMethod}</p>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm">
                        {confirmation.order && (
                          <p className="text-gray-900">Order: {confirmation.order.orderNumber}</p>
                        )}
                        {confirmation.appointment && (
                          <p className="text-gray-900">
                            Appointment: {new Date(confirmation.appointment.date).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(confirmation.status)}`}>
                        {getStatusIcon(confirmation.status)}
                        <span className="ml-1 capitalize">{confirmation.status}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-600">
                      {formatDate(confirmation.createdAt)}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setSelectedConfirmation(confirmation)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                          title="View Details"
                        >
                          <FiEye className="w-4 h-4" />
                        </button>
                        
                        {confirmation.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleStatusUpdate(confirmation._id, 'verified')}
                              className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200"
                              title="Verify"
                            >
                              <FiCheck className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => {
                                const reason = prompt('Rejection reason (optional):');
                                handleStatusUpdate(confirmation._id, 'rejected', reason || '');
                              }}
                              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                              title="Reject"
                            >
                              <FiX className="w-4 h-4" />
                            </button>
                          </>
                        )}
                        
                        <button
                          onClick={() => handleDeleteConfirmation(confirmation._id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                          title="Delete"
                        >
                          <FiTrash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Payment Confirmation Detail Modal */}
      {selectedConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Payment Confirmation Details</h3>
                <button
                  onClick={() => setSelectedConfirmation(null)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  <FiX className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Payment Information</h4>
                  <div className="space-y-3 text-sm">
                    <div>
                      <p className="text-gray-600">Amount:</p>
                      <p className="text-2xl font-bold text-gray-900">
                        ${selectedConfirmation.amount.toFixed(2)}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Payment Method:</p>
                      <p className="font-medium">{selectedConfirmation.paymentMethod}</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Status:</p>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedConfirmation.status)}`}>
                        {getStatusIcon(selectedConfirmation.status)}
                        <span className="ml-1 capitalize">{selectedConfirmation.status}</span>
                      </span>
                    </div>
                    <div>
                      <p className="text-gray-600">Submitted:</p>
                      <p className="font-medium">{formatDate(selectedConfirmation.createdAt)}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Customer Information</h4>
                  <div className="space-y-3 text-sm">
                    <div>
                      <p className="text-gray-600">Name:</p>
                      <p className="font-medium">
                        {selectedConfirmation.user?.firstName} {selectedConfirmation.user?.lastName}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600">Email:</p>
                      <p className="font-medium">{selectedConfirmation.user?.email}</p>
                    </div>
                    {selectedConfirmation.order && (
                      <div>
                        <p className="text-gray-600">Order:</p>
                        <p className="font-medium">{selectedConfirmation.order.orderNumber}</p>
                      </div>
                    )}
                    {selectedConfirmation.appointment && (
                      <div>
                        <p className="text-gray-600">Appointment:</p>
                        <p className="font-medium">
                          {new Date(selectedConfirmation.appointment.date).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Payment Proof */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-4">Payment Proof</h4>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setSelectedImage(selectedConfirmation.proofImage)}
                    className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors duration-200"
                  >
                    <FiEye className="w-4 h-4 mr-2" />
                    View Image
                  </button>
                  <a
                    href={selectedConfirmation.proofImage}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                  >
                    <FiExternalLink className="w-4 h-4 mr-2" />
                    Open in New Tab
                  </a>
                </div>
              </div>

              {/* Notes */}
              {selectedConfirmation.notes && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Notes</h4>
                  <p className="text-gray-700 bg-gray-50 rounded-lg p-4">
                    {selectedConfirmation.notes}
                  </p>
                </div>
              )}

              {/* Verification Info */}
              {selectedConfirmation.status !== 'pending' && (
                <div className="border-t border-gray-200 pt-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Verification Details</h4>
                  <div className="text-sm space-y-2">
                    <p>
                      <span className="text-gray-600">
                        {selectedConfirmation.status === 'verified' ? 'Verified' : 'Rejected'} on:
                      </span>
                      <span className="ml-2 font-medium">
                        {formatDate(selectedConfirmation.verifiedAt)}
                      </span>
                    </p>
                    {selectedConfirmation.verifiedBy && (
                      <p>
                        <span className="text-gray-600">By:</span>
                        <span className="ml-2 font-medium">
                          {selectedConfirmation.verifiedBy.firstName} {selectedConfirmation.verifiedBy.lastName}
                        </span>
                      </p>
                    )}
                    {selectedConfirmation.rejectionReason && (
                      <div>
                        <p className="text-gray-600">Rejection Reason:</p>
                        <p className="text-red-700 bg-red-50 rounded-lg p-3 mt-1">
                          {selectedConfirmation.rejectionReason}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {selectedConfirmation.status === 'pending' && (
                <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                  <button
                    onClick={() => {
                      const reason = prompt('Rejection reason (optional):');
                      handleStatusUpdate(selectedConfirmation._id, 'rejected', reason || '');
                    }}
                    className="px-6 py-2 text-red-600 border border-red-300 rounded-lg hover:bg-red-50 transition-colors duration-200"
                  >
                    Reject
                  </button>
                  <button
                    onClick={() => handleStatusUpdate(selectedConfirmation._id, 'verified')}
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                  >
                    Verify Payment
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute top-4 right-4 p-2 bg-white rounded-full text-gray-600 hover:text-gray-800 transition-colors duration-200"
            >
              <FiX className="w-6 h-6" />
            </button>
            <img
              src={selectedImage}
              alt="Payment proof"
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminPaymentConfirmations;
